package me.socure.dashboard.model

case class WatchlistTranslateResponse(
                                         accountId: String,
                                         transactionId: String,
                                         translatedTexts: List[TranslatedText]
                                     )

case class TranslatedText(
                             id: Option[String],
                             texts: List[String]
                         )