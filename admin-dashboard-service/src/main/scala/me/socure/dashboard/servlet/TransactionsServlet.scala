package me.socure.dashboard.servlet

import com.typesafe.config.Config
import me.socure.account.client.dashboard.SubAccountService.SubAccountClient
import me.socure.account.client.dashboard.V2ValidationClient
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.admindashboard.authentication.stratgies.BaseDashboardAuthServlet
import me.socure.admindashboard.authentication.{AuthenticationService, PermissionChecker}
import me.socure.batch.job.client.BatchJobClient
import me.socure.batch.job.common.constants.{JobAttributes, JobTypes}
import me.socure.batch.job.model.{BatchJob, JobInstanceAttributes}
import me.socure.common.clock.Clock
import me.socure.common.exception.AuthenticationException
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.pii.mask.{ParamsMaskService, PiiMaskService, ResponseMaskService}
import me.socure.common.slick.domain.{Pagination, Sort}
import me.socure.constants.EnvironmentConstants.EnvironmentConstants
import me.socure.constants.{DashboardUserPermissions, EnvironmentConstants, EnvironmentTypes}
import me.socure.dashboard.blacklist.BlacklistService
import me.socure.dashboard.dao.{TransactionReplayerInput, TransactionSearchInput}
import me.socure.dashboard.docv.DocVService
import me.socure.dashboard.error.DashboardTransactionErrors
import me.socure.dashboard.idplus.IdPlusPassThroughClient
import me.socure.dashboard.model._
import me.socure.dashboard.services.{ExtendedTransactionDetailsExtractor, TransactionService}
import me.socure.dashboard.settings.exception.codes.EventManagerExceptionCodes
import me.socure.dashboard.util.{DateParser, ErrorMessage}
import me.socure.entity.monitoring.client.EntityMonitoringClient
import me.socure.entity.monitoring.common.model.EntityResponse
import me.socure.file.storage.common.DocumentFacets
import me.socure.file.storage.download.client.FileDownloadClient
import me.socure.me.socure.dashboard.usermgt.validator.ClearTextPatternValidator
import me.socure.mfa.orchestrator.{ApiException => MfaApiException, Configuration => MfaConfiguration}
import me.socure.mfa.orchestrator.client.{DefaultApi => MfaDefaultApi}
import me.socure.mfa.orchestrator.client.model.SuccessOTPVerificationListAPIResponse.{StatusEnum => MfaStatusEnum}
import me.socure.mfa.orchestrator.client.model.{VerificationEventsListRequest, VerificationsListRequest}
import me.socure.model.account.ApiKeyStatus
import me.socure.model.dashboardv2.{Creator, SubAccountV2}
import me.socure.model.user.DashboardUserRole
import me.socure.model.user.authorization.UserAuth
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.support.timeout.{ApiTimeout, FutureWithTimeoutSupport}
import me.socure.transaction.resolved.entity.client.TransactionResolvedEntityServiceClient
import me.socure.transaction.resolved.entity.common.model.PIIFieldName.PIIFieldName
import me.socure.transaction.resolved.entity.common.model.{BestMatchEntityFilterParams, BestMatchEntityResponse, BestMatchEntitySearchRequest, PIIFieldName}
import me.socure.transaction_case.workflow.common.enums.{EntityDispositionStatus, EntityEscalationStatus, EntityWorkflowStatus}
import me.socure.transaction_case.workflow.common.util.TransformationUtil.getEntityMatchesMap
import me.socure.transaction_case.workflow.service.client.TxnCaseWorkflowServiceClient
import me.socure.transactionauditing.common.domain.{ApiTransaction, ApiTransactionResult, TransactionSearchRequest}
import me.socure.transactionauditing.common.json.TrxJsonFormats
import me.socure.transactionauditing.common.transaction.{TransactionColumnFixtures, TransactionColumns}
import me.socure.transactionauditing.rest.client.TransactionRestClient
import me.socure.translator.{ApiException => TranslatorApiException, Configuration => TranslatorConfiguration}
import me.socure.translator.client.{DefaultApi => TranslatorDefaultApi}
import me.socure.translator.client.model.SuccessResponseWrapper.{StatusEnum => TranslatorStatusEnum}
import me.socure.translator.client.model.{OriginalTexts, SuccessResponseWrapper, TranslateRequest, TranslateResponse}
import me.socure.user.client.dashboard2.SubAccountManagementClient
import me.socure.watchlist.common.WatchlistSearchDetailRequest
import me.socure.watchlist.common.v2.SnapshotViewEntityResponse
import me.socure.watchlist.rest.client.WatchlistServiceClient
import org.apache.commons.io.IOUtils
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import org.json4s.JsonAST.{JField, JValue}
import org.json4s.ext.EnumNameSerializer
import org.json4s.jackson.{JsonMethods, Serialization}
import org.json4s.{DefaultFormats, Formats, JArray, JString}
import org.scalatra.{AsyncResult, BadRequest}
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate
import me.socure.dynamic.control.center.v2.response.EvaluateResponse

import scala.collection.JavaConverters._
import java.io.InputStream
import javax.servlet.http.{HttpServletRequest, HttpServletResponse}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal
import scala.util.{Failure, Success, Try}

/**
 * Created by joonkang on 6/8/16.
 */
class TransactionsServlet(val authService: AuthenticationService,
                          val transactionRestClient: TransactionRestClient,
                          val blacklistService: BlacklistService,
                          subAccountManagementClient: SubAccountManagementClient,
                          clock: Clock,
                          extendedTransactionDetailsExtractor: ExtendedTransactionDetailsExtractor,
                          fileDownloadClient: FileDownloadClient,
                          subAccountClient: SubAccountClient,
                          transactionService: TransactionService,
                          batchJobClient: BatchJobClient,
                          watchlistServiceClient: WatchlistServiceClient,
                          entityMonitoringClient: EntityMonitoringClient,
                          transactionResolvedEntityClient: TransactionResolvedEntityServiceClient,
                          txnCaseWorkflowServiceClient: TxnCaseWorkflowServiceClient,
                          v2ValidationClient: V2ValidationClient,
                          docVService: DocVService,
                          idplusClient: IdPlusPassThroughClient,
                          sandboxClient: IdPlusPassThroughClient,
                          config: Config)(implicit val executor: ExecutionContext, val apiTimeout: ApiTimeout) extends BaseDashboardAuthServlet with FutureWithTimeoutSupport {
  override protected implicit def jsonFormats: Formats = TrxJsonFormats.value ++ List(
    new EnumNameSerializer(EntityDispositionStatus),
    new EnumNameSerializer(EntityWorkflowStatus),
    new EnumNameSerializer(EntityEscalationStatus)
  )

  type FindTransactionResult = Either[ErrorResponse, Any] with Product with Serializable

  override val metrics: Metrics = JavaMetricsFactory.get("admindashboard." + this.getClass.getSimpleName)
  val Sandbox_Name = "Sandbox"

  val dateformat = "yyyy-MM-dd"

  val DEFAULT_PAGE = 1
  val DEFAULT_PAGE_SIZE = 100

  val THIS_PROGRAM = "1"
  val ALL_PROGRAMS = "2"
  val THIS_ACCOUNT = "3"
  val ALL_SUB_ACCOUNT = "4"

  private val NATIONAL_ID = "nationalid"
  private val PREFILL = "prefill"
  private val SSN_First_5= "ssnFirst5"

  val bmeAllowedFields: Array[String] = Array("ssn", "dob")
  val queryAllowedFields: Array[String] = Array(NATIONAL_ID, "dob", "ipaddress", "transaction-query-pii")


  private[servlet] val TransactionListColumns = Set(
    TransactionColumns.ACCOUNT_ID,
    TransactionColumns.API_KEY,
    TransactionColumns.TRANSACTION_DATE,
    TransactionColumns.ERROR,
    TransactionColumns.DEBUG,
    TransactionColumns.PARAMETERS,
    TransactionColumns.PROCESSING_TIME,
    TransactionColumns.API_NAME,
    TransactionColumns.API,
    TransactionColumns.KYC,
    TransactionColumns.OFAC,
    TransactionColumns.API_RESPONSE
  )

  val DOCUMENT_TYPE = "documentType"
  val DOCUMENT_VERIFICATION = "documentVerification"
  val SECONDARY_DOCUMENT = "secondaryDocument"
  val SELFIE_REVERIFICATION = "selfieReverification"
  val COUNTRY = "country"
  val STATE = "state"
  val TYPE = "type"

  def permissionCheck(role: DashboardUserRole.DashboardUserRole): AsyncResult = {
    val environment = params.get("environment")
    val account = params.get("accountId")

    callAccountServiceWithSessionUser(u => {
      val env: Option[EnvironmentConstants] = environment.map(p ⇒ EnvironmentConstants.withName(p))
      val envOpt = PermissionChecker.resolveEnvironment(u, role, env)

      getAccountId(sessionAccountId = u.account.id, accountId = account) flatMap {
        case Right(x) =>
          envOpt.size match {
            case x if x > 0 => Future.successful(Right(u))
            case _ => Future.successful(Left(ErrorMessage.PermissionMissing))
          }
          Future.successful(Right(x))
        case Left(error) => Future.failed(new Exception(error.message))
      }
    })
  }

  private def isLoginAsBSAOfficer(environmentId: Int, sessionUserId: Long, sponsorBankId: Long): Future[Either[ErrorResponse, Boolean]] = {
    subAccountManagementClient.validateAccountAccess(environmentId, sponsorBankId, sessionUserId, DashboardUserPermissions.CHANGE_REQUESTS_VIEW.id.toString)
  }

  private def getAllProgramIdsOfSpBank(sessionAccountId: Long, sessionUserId: Long, isProgram: Option[Boolean], environmentId: Option[Int] = None): Future[Either[ErrorResponse, Set[Long]]] = {
    isProgram match {
      case Some(true) =>
        subAccountManagementClient.getSponsorBankIdsByProgramId(sessionAccountId).flatMap {
          case Right(sponsorBankId) =>
            isLoginAsBSAOfficer(environmentId.getOrElse(EnvironmentTypes.PRODUCTION_ENVIRONMENT.id), sessionUserId, sponsorBankId).flatMap {
              case Right(true) =>
                subAccountManagementClient.getSponsorBankPrograms(sponsorBankId).map {
                  case Right(programs) => Right(programs.map(_.id).toSet)
                  case Left(errorResponse) => logger.error(s"Unable to get programs for $sponsorBankId: $errorResponse")
                    Right(Set.empty)
                }
              case _ => Future.successful(Right(Set.empty))
            }
          case Left(errorResponse) =>
            logger.error(s"Unable to get sponsor bank details for program $sessionAccountId: $errorResponse")
            Future.successful(Right(Set.empty))
        }
      case _ => Future.successful(Right(Set.empty))
    }
  }

  def jsonStringToString(jvalue: JValue): String = {
    jvalue.toOption match {
      case Some(string) => string.values.toString
      case _ => ""
    }
  }

  def getMonitoredTransactions(userAuth: UserAuth, envId: Int, accountId: String, subscriptionStatus: String, sortDir: String, perPage: Option[Int], offset: Option[Int]): Future[Either[ErrorResponse, Seq[TransactionListItemExtd]]] = {
    entityMonitoringClient.getMonitors(envId.toString, accountId, subscriptionStatus, None, None, sortDir, perPage.getOrElse(50).toString, offset.getOrElse(0).toString) flatMap {
      case Right(res) =>
        val txnIds = res.referenceIds.entities
        if (txnIds.nonEmpty) {
          transactionRestClient.fetchTrxList(txnIds.toSet, TransactionColumnFixtures.AllTransactionColumns, maskPii = true) map { transactionsResult => {
            if (transactionsResult.status == 200) {
              Right(transactionsResult.response map (t => {
                val transaction: ApiTransaction = if (userAuth.account.permission.contains(BusinessUserRoles.MASK_PII_ON_VIEW.id)) maskPii(t) else t
                val inputParameters = transaction.requestProps.parameters.getOrElse("{}")
                val inputPayloadObj = parse(inputParameters)
                val modules = inputPayloadObj \ "modules" match {
                  case JArray(values) => values.map(module => module.values.toString).toSet
                  case _ => Set.empty[String]
                }
                val firstName = jsonStringToString(inputPayloadObj \ "firstname")
                val surName = jsonStringToString(inputPayloadObj \ "surname")
                val fullName = jsonStringToString(inputPayloadObj \ "fullname")
                val email = jsonStringToString(inputPayloadObj \ "email")
                val mobileNumber = jsonStringToString(inputPayloadObj \ "mobilenumber")
                val userId = jsonStringToString(inputPayloadObj \ "userId")
                val entityName = jsonStringToString(inputPayloadObj \ "entityName")
                val decisionResult = jsonStringToString(inputPayloadObj \ "decisionResult")
                TransactionListItemExtd(transaction.transactionId, transaction.transactionDate.getOrElse(DateTime.now()),
                  transaction.apiName.getOrElse(""), transaction.productName.getOrElse(""), transaction.error.getOrElse(false),
                  transaction.processingTime.getOrElse(0), transaction.status.map(status => if (status.equals("Successful")) "true" else "false"), transaction.kyc.getOrElse(false),
                  Some(modules), Some(envId), Some(firstName), Some(surName), Some(fullName), Some(email), Some(mobileNumber),
                  transaction.requestProps.customerUserId, Some(userId), Some(entityName), Some(decisionResult), transaction.accountId
                )
              }
                ))
            } else {
              Left(ErrorMessage.ErrorInvokingTransactionAuditingFetch)
            }
          }
          }
        } else {
          Future.successful((Right(Seq.empty[TransactionListItemExtd])))
        }
      case Left(err) =>
        logger.error("Error while retrieving Monitored Transaction List", err)
        Future.successful(Left(ErrorResponse(EventManagerExceptionCodes.CannotFetchMonitoredTransactions.id, EventManagerExceptionCodes.CannotFetchMonitoredTransactions.description)))
    }
  }

  post("/search") {
    Try(parsedBody.extract[TransactionSearchInput]) match {
      case Success(transactionSearchInput) =>
        callAccountServiceWithSessionUser(u => {
          val environment: Option[String] = transactionSearchInput.environmentType
          val env: Option[EnvironmentConstants] = environment.map(p ⇒ EnvironmentConstants.withName(p))
          val creatorAccountId = u.account.id
          val creatorUserId = u.user.id
          val accountId = transactionSearchInput.accountId

          def handleSearchType(transactionSearchInput: TransactionSearchInput, accountId: String): Future[Either[ErrorResponse, List[TransactionListItemExtd]]] = {
            transactionSearchInput.searchType match {
              case Some(ALL_PROGRAMS) =>
                getAllProgramIdsOfSpBank(u.account.id, u.user.id, u.isProgram).flatMap {
                  case Right(programIds) if programIds.nonEmpty =>
                    transactionService.search(transactionSearchInput.copy(accountId = accountId, subAccountIds = Some(programIds)), u.account.permission)
                  case Right(_) => Future.successful(Right(List.empty))
                  case Left(e) => Future.successful(Left(e))
                }
              case Some(ALL_SUB_ACCOUNT) =>
                subAccountManagementClient.getSubAccountsV2(u.account.id, u.user.id, u.account.id, None, None, true).flatMap {
                  case Right(list) if list.nonEmpty =>
                    val subAccounts = list.map(_.id).toSet + u.account.id
                    val userAccountIds = u.accounts.map(_.id)
                    val subAccountWithUserAccess = subAccounts.filter(subAccount => userAccountIds.contains(subAccount))
                    transactionService.search(transactionSearchInput.copy(accountId = accountId, subAccountIds = Some(subAccountWithUserAccess)), u.account.permission)
                  case Right(_) => transactionService.search(transactionSearchInput.copy(accountId = accountId), u.account.permission)
                  case Left(e) => Future.successful(Left(e))
                }
              case _ => transactionService.search(transactionSearchInput.copy(accountId = accountId), u.account.permission)
            }
          }

          def searchTransactions0() = {
            val envOpt = PermissionChecker.resolveEnvironment(u, DashboardUserRole.LIST_TRANSACTION, env)
            envOpt.size match {
              case l if l > 0 =>
                getAccountId(sessionAccountId = u.account.id, accountId = Some(accountId)) flatMap {
                  case Right(x) =>
                    if (transactionSearchInput.fetchMonitoredTransaction) {
                      getMonitoredTransactions(u, env.get.id, accountId, "enabled", "desc", transactionSearchInput.size, transactionSearchInput.start)
                    } else {
                      handleSearchType(transactionSearchInput, accountId)
                    }
                  case _ =>
                    logger.error("Problem fetching the account info from session")
                    Future.successful(Left(ErrorResponseFactory.get(SubAccountNotFound)))
                }
              case _ => Future.successful(Left(ErrorMessage.PermissionMissing))
            }
          }

          if (u.account.permission.contains(BusinessUserRoles.SAML_2_0.id) && !u.account.permission.contains(BusinessUserRoles.NEW_SAML.id)) { // OLD SAML
            searchTransactions0()
          } else if (u.account.permission.contains(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)) {
            isValidAccountAccess(creatorAccountId, creatorUserId, accountId.toLong) flatMap {
              case Right(true) => env match {
                case Some(e) =>
                  subAccountManagementClient.validateAccountAccess(e.id, creatorAccountId, creatorUserId, DashboardUserPermissions.TRANSACTIONS_VIEW.id.toString) flatMap {
                    case Right(true) =>
                      if (transactionSearchInput.fetchMonitoredTransaction) {
                        getMonitoredTransactions(u, e.id, accountId, "enabled", "desc", transactionSearchInput.size, transactionSearchInput.start)
                      } else {
                        handleSearchType(transactionSearchInput, accountId)
                      }
                    case Left(e) =>
                      logger.error(s"Invalid account access for Account:$creatorAccountId, user:$creatorUserId", e)
                      Future.successful(Left(e))
                  }
                case None =>
                  logger.error(s"Invalid environment provided for Account:$creatorAccountId, user:$creatorUserId")
                  Future.successful(Left(ErrorMessage.InvalidEnvironment))
              }
              case Left(e) => Future.successful(Left(e))
            }
          } else {
            searchTransactions0()
          }
        })
      case Failure(NonFatal(ex)) =>
        logger.error("Invalid request format", ex)
        Future.successful(BadRequest(body = "Invalid request format"))
    }
  }

  private def exportTransactions(transactionExportInput: TransactionSearchInput, u: UserAuth): Future[Either[ErrorResponse, String]] = {
    val environment = transactionExportInput.environmentType match {
      case Some("Development") => "2"
      case Some("Sandbox") => "3"
      case Some("Production") => "1"
      case _ => "1"
    }
    if (transactionExportInput.fieldsToExport.isEmpty) {
      Future.successful(Left(ErrorResponse(400, "Fields to be exported are empty")))
    } else {
      val jobAttributes = List(
        JobInstanceAttributes(JobAttributes.ACCOUNTID.id, u.account.id.toString),
        JobInstanceAttributes(JobAttributes.CREATEDAT.id, (System.currentTimeMillis / 1000).toString),
        JobInstanceAttributes(JobAttributes.CREATEDBY.id, u.user.email),
        JobInstanceAttributes(JobAttributes.REPORTNAME.id, transactionExportInput.reportName.getOrElse("")),
        JobInstanceAttributes(JobAttributes.REPORTFILEFORMAT.id, transactionExportInput.reportFileFormat.getOrElse("")),
        JobInstanceAttributes(JobAttributes.ENVIRONMENT.id, environment)
      )
      val isDocVConsentAudit: Boolean = (transactionExportInput.fieldsToExport.size == 1 && transactionExportInput.fieldsToExport.head.equalsIgnoreCase("consent-audit-export"))

      val searchParams = Map("accountId" -> u.account.id.toString, "search" -> transactionExportInput, "isDocVConsentAudit" -> isDocVConsentAudit)
      val jsonSearchInput = org.json4s.jackson.Serialization.write(searchParams)

      val batchJob = BatchJob(`type` = JobTypes.getIdByValue(JobTypes.EXPORT), parameter = Some(jsonSearchInput), jobAttributes)
      batchJobClient.createJob(batchJob)
    }
  }

  post("/export") {
    Try(parsedBody.extract[TransactionSearchInput]) match {
      case Success(transactionExportInput) =>
        callAccountServiceWithSessionUser(u => {
          val environment: Option[String] = transactionExportInput.environmentType
          val env: Option[EnvironmentConstants] = environment.map(p ⇒ EnvironmentConstants.withName(p))
          val creatorAccountId = u.account.id
          val creatorUserId = u.user.id
          val accountId = transactionExportInput.accountId
          val validName = transactionExportInput.reportName match {
            case Some(name) if name.nonEmpty =>
              ClearTextPatternValidator.validateCompanyName(name)
            case _ =>
              true
          }

          def exportTransactions0() = {
            val envOpt = PermissionChecker.resolveEnvironment(u, DashboardUserRole.LIST_TRANSACTION, env)
            envOpt.size match {
              case l if l > 0 =>
                getAccountId(sessionAccountId = u.account.id, accountId = Some(transactionExportInput.accountId)) flatMap {
                  case Right(_) =>
                    exportTransactions(transactionExportInput, u)
                  case _ =>
                    logger.error("Problem fetching the account info from session")
                    Future.successful(Left(ErrorResponseFactory.get(SubAccountNotFound)))
                }
              case _ => Future.successful(Left(ErrorMessage.PermissionMissing))
            }
          }

          if (validName) {
            if (u.account.permission.contains(BusinessUserRoles.SAML_2_0.id) && !u.account.permission.contains(BusinessUserRoles.NEW_SAML.id)) { // OLD SAML
              exportTransactions0()
            } else if (u.account.permission.contains(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)) {
              isValidAccountAccess(creatorAccountId, creatorUserId, accountId.toLong) flatMap {
                case Right(true) =>
                  v2ValidationClient.isPermissionAvailable(accountId.toLong, Set(DashboardUserPermissions.REPORTING_CREATE.id), Creator(u.user.id, u.account.id)) flatMap {
                    case Right(true) => exportTransactions(transactionExportInput, u)
                    case Left(e) =>
                      logger.error(s"Invalid account access for Account:$creatorAccountId, user:$creatorUserId", e)
                      Future.successful(Left(e))
                  }
                case Left(e) => Future.successful(Left(e))
              }
            } else {
              exportTransactions0()
            }
          } else {
            logger.error("Given report name is invalid")
            Future.successful(Left(ErrorResponse(ExceptionCodes.InvalidParametersPassed.id, ExceptionCodes.InvalidParametersPassed.description)))
          }
        })
      case Failure(NonFatal(ex)) =>
        logger.error("Invalid request format", ex)
        Future.successful(BadRequest(body = "Invalid request format"))
    }
  }

  private def replayTransactions(replayType: Int, transactionReplayerInput: TransactionReplayerInput, u: UserAuth): Future[Either[ErrorResponse, String]] = {
    if (replayType == -1) {
      logger.error(s"No permission to replay transactions:$replayType $transactionReplayerInput")
      Future.successful(Left(ErrorMessage.PermissionMissing))
    } else {
      val inputParams = org.json4s.jackson.Serialization.write(transactionReplayerInput.copy(replayType = replayType))
      val jobAttributes = List(
        JobInstanceAttributes(JobAttributes.ACCOUNTID.id, u.account.id.toString),
        JobInstanceAttributes(JobAttributes.CREATEDAT.id, (System.currentTimeMillis / 1000).toString),
        JobInstanceAttributes(JobAttributes.CREATEDBY.id, u.user.email)
      )
      val batchJob = BatchJob(`type` = JobTypes.getIdByValue(JobTypes.TRANSACTION_REPLAY), parameter = Some(inputParams), jobAttributes)
      batchJobClient.createJob(batchJob)
    }
  }

  private def validateInput(transactionReplayerInput: TransactionReplayerInput): Boolean =
    transactionReplayerInput.dateRange.isDefined ||
      (transactionReplayerInput.startDate.isDefined && transactionReplayerInput.endDate.isDefined)

  post("/replay") {
    Try(parsedBody.extract[TransactionReplayerInput]) match {
      case Success(transactionReplayerInput) =>
        validateInput(transactionReplayerInput) match {
          case true => {
            callAccountServiceWithSessionUser(u => {
              val environment: Option[String] = transactionReplayerInput.environmentType
              val env: Option[EnvironmentConstants] = environment.map(p ⇒ EnvironmentConstants.withName(p))
              val creatorAccountId = u.account.id
              val creatorUserId = u.user.id
              val accountId = transactionReplayerInput.accountId
              val replayType = (u.account.permission.contains(BusinessUserRoles.AlertlistRunnerConsortium.id),
                u.account.permission.contains(BusinessUserRoles.AlertlistRunnerBancorp.id)) match {
                case (true, false) => 1
                case (false, true) => 2
                case (true, true) => 3
                case (false, false) => -1
              }

              def replayTransactions0() = {
                val envOpt = PermissionChecker.resolveEnvironment(u, DashboardUserRole.LIST_TRANSACTION, env)
                envOpt.size match {
                  case l if l > 0 =>
                    getAccountId(sessionAccountId = u.account.id, accountId = Some(transactionReplayerInput.accountId)) flatMap {
                      case Right(_) =>
                        replayTransactions(replayType, transactionReplayerInput, u)
                      case _ =>
                        logger.error("Problem fetching the account info from session")
                        Future.successful(Left(ErrorResponseFactory.get(SubAccountNotFound)))
                    }
                  case _ =>
                    logger.error(s"No permission to replay transactions:$replayType $transactionReplayerInput")
                    Future.successful(Left(ErrorMessage.PermissionMissing))
                }
              }

              if (u.account.permission.contains(BusinessUserRoles.SAML_2_0.id) && !u.account.permission.contains(BusinessUserRoles.NEW_SAML.id)) { // OLD SAML
                replayTransactions0()
              } else if (u.account.permission.contains(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)) {
                isValidAccountAccess(creatorAccountId, creatorUserId, accountId.toLong) flatMap {
                  case Right(true) => env match {
                    case Some(e) =>
                      subAccountManagementClient.validateAccountAccess(e.id, creatorAccountId, creatorUserId, DashboardUserPermissions.TRANSACTIONS_VIEW.id.toString) flatMap {
                        case Right(true) => replayTransactions(replayType, transactionReplayerInput, u)
                        case Left(e) =>
                          logger.error(s"Invalid account access for Account:$creatorAccountId, user:$creatorUserId", e)
                          Future.successful(Left(e))
                      }
                    case None =>
                      logger.error(s"Invalid environment provided for Account:$creatorAccountId, user:$creatorUserId")
                      Future.successful(Left(ErrorMessage.InvalidEnvironment))
                  }
                  case Left(e) => Future.successful(Left(e))
                }
              } else {
                replayTransactions0()
              }
            })
          }
          case false => {
            logger.error(s"Date parameters are not set:$transactionReplayerInput")
            Future.successful(BadRequest(body = "Invalid request format"))
          }
        }
      case Failure(NonFatal(ex)) =>
        logger.error("Invalid request format", ex)
        Future.successful(BadRequest(body = "Invalid request format"))
    }
  }

  get("/by_id/:transactionId") {
    val transactionColumns = TransactionColumnFixtures.AllTransactionColumns
    val txid = params("transactionId")

    callAccountServiceWithSessionUser { u =>
      retrieveTransactionInfo(txid, transactionColumns, u).flatMap {
        case Left(e) => Future.successful(Left(e))

        case Right(transactionInfo) =>
          val needsThirdPartyData = transactionInfo.base.apiResponse.exists(response =>
            response.contains(SELFIE_REVERIFICATION) ||
              response.contains(DOCUMENT_VERIFICATION)
          )

          if (needsThirdPartyData) {
            for {
              shouldFetch <- transactionService.shouldEvaluateFetchThirdPartyTransactionData(u.account.id.toString())
              result <- if (shouldFetch) {
                val transactionDate = transactionService.formatDateForThirdPartyCall(transactionInfo.base.transactionDate)
                transactionService
                  .fetchThirdPartyTransactionData(txid, transactionDate, u.account.id)
                  .map {
                    case Right(thirdPartyResult) =>
                      Right(transactionService.updateWithThirdPartyPII(transactionInfo, thirdPartyResult, false))
                    case Left(_) =>
                      // if third party call fails, default to original transaction info and log
                      logger.error(s"Failed to fetch third party transaction information for transaction: ${txid}. Returning the transaction with default information")
                      Right(transactionInfo)
                  }
              } else {
                Future.successful(Right(transactionInfo))
              }
            } yield result
          } else {
            Future.successful(Right(transactionInfo))
          }
      }
    }
  }

  get("/by_id/unmasked_pii/:module/:transactionId") {
    val txid = params("transactionId")
    val module = params("module")
    if (TransactionsServlet.allowedServicesForPiiUnmasking.contains(module)) {
      callAccountServiceWithSessionUser(u => {
        val creatorAccountId = u.account.id
        val creatorUserId = u.user.id
        if (u.account.permission.contains(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) && u.account.permission.contains(BusinessUserRoles.DashboardV3.id)) {
          v2ValidationClient.isPermissionAvailable(u.account.id, Set(DashboardUserPermissions.PII_ACCESS_VIEW.id), Creator(creatorUserId, creatorAccountId)) flatMap {
            case Right(true) => getUnMaskedPIIFields(txid, u, "byid", module)
            case Left(e) =>
              logger.error(s"Invalid account access for Account:$creatorAccountId, user:$creatorUserId", e)
              Future.successful(Left(e))
          }
        } else {
          Future.successful(Left(ErrorResponse(code = 403, message = "Access Denied")))
        }
      })
    } else {
      Future.successful(Left(ErrorResponse(code = 400, message = "Invalid module specified")))
    }
  }

  private def getUnMaskedPIIFields(transactionId: String, userAuth: UserAuth, tag: String, module: String): Future[Either[ErrorResponse, UnmaskedPIIField]] = {
    val futureTransaction: Future[Option[ApiTransaction]] = {
      transactionRestClient.findTransaction(transactionId, Set(TransactionColumns.API_RESPONSE, TransactionColumns.ACCOUNT_ID), maskPii = false).mapAll {
        case Success(t) =>
          t.response.headOption
        case Failure(e) =>
          logger.error("Error response while finding the transactions", e)
          None
      }
    }
    val sessionAccountId = userAuth.account.id
    val sessionUserId = userAuth.user.id
    val futureSubAccountDetails = {
      if (userAuth.account.permission.contains(BusinessUserRoles.SAML_2_0.id) && !userAuth.account.permission.contains(BusinessUserRoles.NEW_SAML.id)) { // OLD SAML
        subAccountManagementClient.getSubAccounts(userAuth.account.id)
      } else if (userAuth.account.permission.contains(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)) {
        subAccountManagementClient.getSubAccountsV2(sessionAccountId, sessionUserId, sessionAccountId, None, None, true)
      } else {
        subAccountManagementClient.getSubAccounts(userAuth.account.id)
      }
    }
    for {
      retrievedOptTrx <- futureTransaction
      listSubAccounts <- futureSubAccountDetails
      listPrograms <- getAllProgramIdsOfSpBank(userAuth.account.id, userAuth.user.id, userAuth.isProgram)
      responseTrxDetails <- fetchSsnAndDobField(retrievedOptTrx, userAuth, listSubAccounts, listPrograms, module)
    } yield responseTrxDetails
  }

  private def fetchSsnAndDobField(trx: Option[ApiTransaction], userAuth: UserAuth, listSubAccounts: Either[ErrorResponse, List[SubAccountV2]], listPrograms: Either[ErrorResponse, Set[Long]], module: String): Future[Either[ErrorResponse, UnmaskedPIIField]] = {
    trx match {
      case Some(trx) =>
        if (!isTrxFromParentOrSubAccount(userAuth.account.id, trx.accountId, listSubAccounts) && !isTrxFromPrograms(listPrograms, trx.accountId)) {
          Future.successful(Left(ErrorResponse(102, "Transaction not made by this account or any of its subaccounts")))
        } else {
          val apiResponse = trx.apiResponse.getOrElse("{}")
          val apiResponseObj = parse(apiResponse)
          val (ssn, dob) = {
            if (module.equals("kycPlus")) {
              (
                if(userAuth.account.permission.contains(BusinessUserRoles.MaskSsnOnView.id)) transactionService.providedPlaceholder else jsonStringToString(apiResponseObj \ "kycPlus" \ "bestMatchedEntity" \ "ssn"),
                jsonStringToString(apiResponseObj \ "kycPlus" \ "bestMatchedEntity" \ "dob")
              )
            } else {
              (
                if(userAuth.account.permission.contains(BusinessUserRoles.MaskSsnOnView.id)) transactionService.providedPlaceholder else jsonStringToString(apiResponseObj \ "deceasedCheck" \ "nationalId"),
                jsonStringToString(apiResponseObj \ "deceasedCheck" \ "dob")
              )
            }
          }
          Future.successful(Right(UnmaskedPIIField(Some(ssn), Some(dob), None, None, None, None)))
        }
      case None => Future.successful(Left(ErrorMessage.ErrorInvokingTransactionAuditingFetch))
    }
  }

  private def fetchBestMatchEntity(transactionId: String, piiFields: Array[PIIFieldName], sessionAccountId: Long, sessionUserId: Long, isProgram: Option[Boolean], isSSNMaskingEnabled: Option[Boolean]): Future[Either[ErrorResponse, BestMatchEntityResponse]] = {
    val searchParam = BestMatchEntitySearchRequest(transactionId, BestMatchEntityFilterParams(piiFields), isSSNMaskingEnabled)
    transactionResolvedEntityClient.getBestMatchEntity(searchParam).flatMap { response =>
        response.response match {
          case Right(bmeResp) =>
            subAccountManagementClient.getSubAccountsV2(sessionAccountId, sessionUserId, sessionAccountId, None, None, true).flatMap { subAccountsListResp =>
              isTrxFromParentOrSubAccount(sessionAccountId, Some(bmeResp.accountId), subAccountsListResp) match {
                case true => Future.successful(Right(bmeResp))
                case false => getAllProgramIdsOfSpBank(sessionAccountId, sessionUserId, isProgram).map { programIds =>
                  if (isTrxFromPrograms(programIds, Some(bmeResp.accountId))) {
                    Right(bmeResp)
                  } else {
                    Left(ErrorResponse(102, "Transaction not made by this account or any of its subaccounts"))
                  }
                }
              }
            }
          case Left(ex) => Future.successful(Left(ex))
        }
      }
  }

  get("/by_id/:transactionId/bme") {
    val txid = params.get("transactionId").getOrElse(halt(400, "TransactionId not provided"))
    val fieldNames = Array(
      PIIFieldName.firstName,
      PIIFieldName.middleName,
      PIIFieldName.surName,
      PIIFieldName.mobileNumber,
      PIIFieldName.emailAddress,
      PIIFieldName.associatedAddresses,
      PIIFieldName.associatedPhoneNumbers,
      PIIFieldName.associatedEmails,
      PIIFieldName.city,
      PIIFieldName.country,
      PIIFieldName.state,
      PIIFieldName.streetAddress,
      PIIFieldName.zipCode,
      PIIFieldName.suffixName,
      PIIFieldName.deceasedDate,
      PIIFieldName.ssnIssued
    )

    callAccountServiceWithSessionUser(u => {
      if (u.account.permission.contains(BusinessUserRoles.BestMatchEntityViaDashboard.id)) {
        val isSSNMaskingEnabled = Some(u.account.permission.contains(BusinessUserRoles.MaskSsnOnView.id))
        fetchBestMatchEntity(txid, fieldNames, u.account.id, u.user.id, u.isProgram, isSSNMaskingEnabled)
      } else {
        Future.successful(Left(ErrorMessage.PermissionMissing))
      }
    })
  }

  get("/by_id/:transactionId/bme/:field") {
    val txid = params.get("transactionId").getOrElse(halt(400, "TransactionId not provided"))
    val fieldName = params.get("field").getOrElse(halt(400, "Field not provided"))

    if (!bmeAllowedFields.contains(fieldName)) {
      halt(400, "Invalid input")
    }

    val fieldNames = Array(PIIFieldName.withName(fieldName))

    def fetchBestMatchEntity0(creatorAccountId: Long, creatorUserId: Long, isProgram: Option[Boolean], isSSNMaskingEnabled: Option[Boolean]) = {
      fetchBestMatchEntity(txid, fieldNames, creatorAccountId, creatorUserId, isProgram, isSSNMaskingEnabled).flatMap {
        case Right(b) =>
          b.bestMatchEntity match {
            case Some(piiData) =>
              if (fieldName == "dob") Future.successful(Right(piiData.dob))
              else if (fieldName == "ssn") Future.successful(Right(piiData.ssn))
              else Future.successful(Right())
            case None => Future.successful(Right())
          }
        case Left(a) => Future.successful(Left(a))
      }
    }

    callAccountServiceWithSessionUser(u => {
      if (u.account.permission.contains(BusinessUserRoles.BestMatchEntityViaDashboard.id)) {
        val creatorAccountId = u.account.id
        val creatorUserId = u.user.id
        val isSSNMaskingEnabled = Some(u.account.permission.contains(BusinessUserRoles.MaskSsnOnView.id))
        if (u.account.permission.contains(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) && u.account.permission.contains(BusinessUserRoles.DashboardV3.id)) {
          v2ValidationClient.isPermissionAvailable(creatorAccountId, Set(DashboardUserPermissions.PII_ACCESS_VIEW.id), Creator(creatorUserId, creatorAccountId)) flatMap {
            case Right(true) => fetchBestMatchEntity0(creatorAccountId, creatorUserId, u.isProgram, isSSNMaskingEnabled)
            case Left(e) =>
              logger.error(s"Invalid account access for Account:$creatorAccountId, user:$creatorUserId", e)
              Future.successful(Left(e))
          }
        } else {
          fetchBestMatchEntity0(creatorAccountId, creatorUserId, u.isProgram, isSSNMaskingEnabled)
        }
      } else {
        Future.successful(Left(ErrorMessage.PermissionMissing))
      }
    })
  }

  get("/by_id/:transactionId/query/:field") {
    val txid = params.get("transactionId").getOrElse(halt(400, "TransactionId not provided"))
    val fieldName = params.get("field").getOrElse(halt(400, "Field not provided"))

    if (!queryAllowedFields.contains(fieldName)) {
      halt(400, "Invalid input")
    }
    def findTransaction(userAuth: UserAuth): Future[FindTransactionResult] = {
      transactionRestClient.findTransaction(
        transactionId = txid,
        Set(TransactionColumns.API_RESPONSE, TransactionColumns.PARAMETERS, TransactionColumns.ACCOUNT_ID),
        maskPii = false
      ).flatMap { apiResponse =>
        isValidTransactionFromParentOrSubAccountOrProgram(userAuth, apiResponse).flatMap {
          case true =>
            if (apiResponse.response.nonEmpty) {
              apiResponse.response.head.requestProps.parameters match {
                case Some(parameters) =>
                  val json = JsonMethods.parse(parameters)
                  if (fieldName == "transaction-query-pii") {
                    var ssn = if (userAuth.account.permission.contains(BusinessUserRoles.MaskSsnOnView.id))
                      transactionService.providedPlaceholder
                    else
                      jsonStringToString(json \ "nationalid")

                    var dob = jsonStringToString(json \ "dob")
                    val ipaddress = jsonStringToString(json \ "ipaddress")
                    val accountNumber = jsonStringToString(json \ "payments" \ "account" \ "accountNumber")
                    val routingNumber = jsonStringToString(json \ "payments" \ "account" \ "routingNumber")
                    val ein = jsonStringToString(json \ "ein")

                    val containsDoc = apiResponse.response.head.apiResponse.exists(response =>
                      response.contains(SELFIE_REVERIFICATION) ||
                        response.contains(DOCUMENT_VERIFICATION)
                    )

                    if (containsDoc) {
                      transactionService.shouldEvaluateFetchThirdPartyTransactionData(userAuth.account.id.toString).flatMap {
                        case true =>
                          transactionService.fetchThirdPartyTransactionData(
                            txid,
                            transactionService.formatDateForThirdPartyCall(apiResponse.response.head.transactionDate),
                            userAuth.account.id
                          ).flatMap {
                            case Right(thirdPartyTransactionData) =>
                              if (thirdPartyTransactionData.nonEmpty) {
                                dob = thirdPartyTransactionData.getOrElse("dob", dob)
                                ssn = if (userAuth.account.permission.contains(BusinessUserRoles.MaskSsnOnView.id))
                                  transactionService.providedPlaceholder
                                else
                                  thirdPartyTransactionData.getOrElse("nationalId", ssn)
                              }
                              Future.successful(Right(UnmaskedPIIField(
                                Some(ssn), Some(dob), Some(ipaddress), Some(accountNumber), Some(routingNumber), Some(ein)
                              )))

                            case Left(e) =>
                              logger.error(s"Failed to fetch third party transaction information for transaction: ${txid}. Returning Default unmasked PII")
                              Future.successful(Right(UnmaskedPIIField(
                                Some(ssn), Some(dob), Some(ipaddress), Some(accountNumber), Some(routingNumber), Some(ein)
                              )))
                          }

                        case false =>
                          Future.successful(Right(UnmaskedPIIField(
                            Some(ssn), Some(dob), Some(ipaddress), Some(accountNumber), Some(routingNumber), Some(ein)
                          )))
                      }
                    } else {
                      Future.successful(Right(UnmaskedPIIField(
                        Some(ssn), Some(dob), Some(ipaddress), Some(accountNumber), Some(routingNumber), Some(ein)
                      )))
                    }
                  } else {
                    Future.successful(Right(json \ fieldName))
                  }

                case None =>
                  Future.successful(Right("No parameters found"))
              }
            } else {
              Future.successful(Right("No response data"))
            }

          case false =>
            Future.successful(Left(ErrorResponse(102, "Transaction not made by this account or any of its subaccounts")))
        }
      }
    }

    callAccountServiceWithSessionUser(u => {
      val creatorAccountId = u.account.id
      val creatorUserId = u.user.id
      if (u.account.permission.contains(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) && u.account.permission.contains(BusinessUserRoles.DashboardV3.id)) {
        v2ValidationClient.isPermissionAvailable(creatorAccountId, Set(DashboardUserPermissions.PII_ACCESS_VIEW.id), Creator(creatorUserId, creatorAccountId)) flatMap {
          case Right(true) => findTransaction(u)
          case Left(e) =>
            logger.error(s"Invalid account access for Account:$creatorAccountId, user:$creatorUserId", e)
            Future.successful(Left(e))
        }
      } else {
        findTransaction(u)
      }
    })
  }

  private def isValidTransactionFromParentOrSubAccountOrProgram(userAuth: UserAuth, apiResponse: ApiTransactionResult): Future[Boolean] = {
    val trxAccountIdOpt = apiResponse.response.headOption.flatMap(_.accountId)
    val sessionAccountId = userAuth.account.id
    val sessionUserId = userAuth.user.id

    trxAccountIdOpt match {
      case Some(trxAccountId) =>
        subAccountManagementClient.getSubAccountsV2(sessionAccountId, sessionUserId, sessionAccountId, None, None, true).flatMap { subAccountResp =>
          isTrxFromParentOrSubAccount(sessionAccountId, Some(trxAccountId), subAccountResp) match {
            case true => Future.successful(true)
            case false => getAllProgramIdsOfSpBank(sessionAccountId, sessionUserId, userAuth.isProgram).map { programIds =>
              isTrxFromPrograms(programIds, Some(trxAccountId))
            }
          }
        }
      case None => Future.successful(false)
    }
  }

  get("/by_id/:transactionId/images") {
    val transactionColumns = TransactionColumnFixtures.AllTransactionColumns
    val transactionId: String = params("transactionId")

    val accountDetailsFeature = callAccountServiceWithSessionUser(u => {
      val transactionInfoFuture: Future[Either[ErrorResponse, ExtendedTransactionDetails]] = retrieveTransactionInfo(transactionId, transactionColumns, u)

      val fileDownloadFuture: Future[Either[ErrorResponse, Seq[UploadedDocumentInfo]]] = transactionInfoFuture.flatMap {
        case Right(trxDetails) =>
          extractDocumentUUID(trxDetails).flatMap {
            case Left(errorResponse) => Future.successful(Left(errorResponse))
            case Right(None) => Future.successful(Left(ErrorResponse(11, "no document found")))
            case Right(Some(documentUUID)) =>
              trxDetails.base.accountId match {
                case None =>
                  Future.successful(Left(DashboardTransactionErrors.InvalidaAccountId))
                case Some(txAccountId) =>
                  fileDownloadClient
                    .listFilesByUuid(txAccountId.toString, documentUUID)
                    .map {
                      case Left(e) =>
                        logger.error(s"Problem in fetching document info=$e")
                        Left(e)
                      case Right(r) =>
                        val responseSeq: Seq[UploadedDocumentInfo] = r.data.map {
                          i => {
                            UploadedDocumentInfo(
                              fileName = i.fileName,
                              fileType = i.fileType.toString,
                              documentSubtype = i.documentSubtype,
                              instanceNumber = i.instanceNumber,
                              uploadNumber = i.uploadNumber
                            )
                          }
                        }
                        Right(responseSeq)
                    }
              }
          }
        case Left(errorResponse) => Future.successful(Left(errorResponse))
      }
      fileDownloadFuture

    })
    accountDetailsFeature.is
  }

  private def validateRequestAndFetchSingleImageStream(localResponse: HttpServletResponse, transactionId: String, fetchImageStreamFn: (Long, String) => Future[Either[ErrorResponse, InputStream]], onErrorMessage: String) = {
    val imageStreamFuture = callAccountServiceWithSessionUserStreamPublic(u => {
      val transactionInfoFuture: Future[Either[ErrorResponse, ExtendedTransactionDetails]] = retrieveTransactionInfo(transactionId, Set(TransactionColumns.PARAMETERS, TransactionColumns.API_KEY, TransactionColumns.ACCOUNT_ID), u)

      val externalClientFuture: Future[Either[ErrorResponse, InputStream]] = transactionInfoFuture flatMap {
        case Right(transactionDetails) =>
          extractDocumentUUID(transactionDetails).flatMap {
            case Left(errorResponse) => Future.successful(Left(errorResponse))
            case Right(None) => Future.successful(Left(ErrorResponse(11, "no document found")))
            case Right(Some(documentUUID)) =>
              transactionDetails.base.accountId match {
                case None =>
                  Future.successful(Left(DashboardTransactionErrors.InvalidaAccountId))
                case Some(accountId) =>
                   v2ValidationClient.isPermissionAvailable(accountId, Set(DashboardUserPermissions.PII_ACCESS_VIEW.id), Creator(u.user.id, u.account.id)) flatMap  {
                    case Right(true) =>
                      val fileStreamFuture: Future[Either[ErrorResponse, InputStream]] = fetchImageStreamFn(accountId, documentUUID)
                      .map {
                        case Right(inputStream) =>
                          Right(inputStream)
                        case Left(_) =>
                          logger.error(onErrorMessage)
                          Left(ErrorResponse(500, onErrorMessage))
                      }
                      fileStreamFuture
                    case Left(error) => logger.error("Error response while checking permission", error.message)
                      Future.successful(Left(error))
                    case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
                  }
              }
          }
      }
      // TODO: we should create a method that accepts Either[ErrorResponse, InputStream] in ScalatraResponseFactory
      // This will allow us to return an error code in addition to message if we wish (not critical)
      externalClientFuture.map(_.left.map(_.message))
    })
    imageStreamFuture.is
  }

  private case class SecondaryDocumentMultiImageResponse(
                                                          images: Seq[String]
                                                        )

  private def validateRequestAndFetchSecondaryImages(transactionId: String, instanceNumber: Int, numUploads: Int) = {
    val responseFuture = callAccountServiceWithSessionUser(u => {
      val transactionInfoFuture: Future[Either[ErrorResponse, ExtendedTransactionDetails]] = retrieveTransactionInfo(transactionId, Set(TransactionColumns.PARAMETERS, TransactionColumns.API_KEY, TransactionColumns.ACCOUNT_ID), u)

      val externalClientFuture: Future[Either[ErrorResponse, SecondaryDocumentMultiImageResponse]] = transactionInfoFuture flatMap {
        case Right(transactionDetails) =>
          extractDocumentUUID(transactionDetails).flatMap {
            case Left(errorResponse) => Future.successful(Left(errorResponse))
            case Right(None) => Future.successful(Left(ErrorResponse(11, "no document found")))
            case Right(Some(documentUUID)) =>
              transactionDetails.base.accountId match {
                case None =>
                  Future.successful(Left(DashboardTransactionErrors.InvalidaAccountId))
                case Some(accountId) =>
                  fileDownloadClient.retrieveBase64Images(accountId.toString, documentUUID, DocumentFacets.UNSTRUCTURED, instanceNumber, numUploads)
                    .map(_.right.map(images => SecondaryDocumentMultiImageResponse(images = images)))
              }
          }
      }
      externalClientFuture
    })
     responseFuture.is

  }

  get("/by_id/:transactionId/images/:imageType") {
    contentType = "application/octet-stream"
    val transactionId: String = params("transactionId")
    val imageType: String = params("imageType")
    val localResponse: HttpServletResponse = response

    val fetchImageStreamFn = (accountId: Long, documentUUID: String) => {
      fileDownloadClient
        .retrieveImageStream(accountId.toString, documentUUID, DocumentFacets.byName(imageType).get)
    }
    val onErrorMsg = s"Problem in fetching image for transaction= $transactionId and field type= $imageType"

    validateRequestAndFetchSingleImageStream(localResponse, transactionId, fetchImageStreamFn, onErrorMsg)
  }


  // endpoint primarily used by secondary documents. In the future, this may be used by additional document types
  get("/by_id/:transactionId/images/:imageType/:instanceNumber/:uploadNumber") {
    contentType = "application/octet-stream"
    val transactionId: String = params("transactionId")
    val imageType: String = params("imageType")
    val instanceNumber = params("instanceNumber").toInt
    val uploadNumber = params("uploadNumber").toInt
    val localResponse: HttpServletResponse = response

    val fetchImageStreamFn = (accountId: Long, documentUUID: String) => {
      fileDownloadClient
        .retrieveImageStream(accountId.toString, documentUUID, DocumentFacets.byName(imageType).get, instanceNumber, uploadNumber)
    }
    val onErrorMsg = s"Problem in fetching image for transaction= $transactionId and " +
      s"field type= $imageType with instance number=$instanceNumber and upload number=$uploadNumber"

    validateRequestAndFetchSingleImageStream(localResponse, transactionId, fetchImageStreamFn, onErrorMsg)
  }

  // endpoint to fetch converted images for secondary documents. This endpoint is intended to be temporary as performance
  // is poor.
  get("/by_id/:transactionId/secondary_images/:instanceNumber/:numUploads") {
    val transactionId: String = params("transactionId")
    val instanceNumber = params("instanceNumber").toInt
    val numUploads = params("numUploads").toInt

    validateRequestAndFetchSecondaryImages(transactionId, instanceNumber, numUploads)
  }

  private def extractDocumentUUID(extendedTransactionDetails: ExtendedTransactionDetails): Future[Either[ErrorResponse, Option[String]]] = {
    docVService.extractOrFetchDocumentUUID(extendedTransactionDetails).map {
      case Left(errorResponse) =>
        logger.error(s"could not fetch document UUID ${errorResponse.message}")
        Left(ErrorResponse(11, "Internal Server Error"))
      case Right(documentUUID) => Right(documentUUID)
    }
  }

  private def retrieveTransactionInfo(transactionId: String, transactionColumns: Set[TransactionColumns.TransactionColumn],
                                      userAuth: UserAuth): Future[Either[ErrorResponse, ExtendedTransactionDetails]] = {
    logger.info(s"Querying transactions by transaction id: $transactionId")
    val futureTransaction: Future[Option[ApiTransaction]] = {
      transactionRestClient.findTransaction(transactionId, transactionColumns, maskPii = true).mapAll {
        case Success(t) =>
          val canAccessPII: Future[Boolean] = v2ValidationClient.isPermissionAvailable(userAuth.account.id, Set(DashboardUserPermissions.PII_ACCESS_VIEW.id), Creator(userAuth.user.id, userAuth.account.id)) map {
            case Left(error) => logger.error("Error response while checking permission", error.message)
              false
            case Right(boolean: Boolean) => boolean
          }

          if (userAuth.account.permission.contains(BusinessUserRoles.MASK_PII_ON_VIEW.id)) {
            Future.successful(t.response.headOption.map(maskPii))
          } else if (userAuth.account.permission.contains(BusinessUserRoles.MaskSsnOnView.id)) {
            canAccessPII map {
              case false =>
                t.response.headOption.map(maskPiiForPiiAccessView).map(maskSSNFromRequestProps).map(maskSSNFromPrefillData)
              case true =>
                t.response.headOption.map(maskSSNFromRequestProps).map(maskSSNFromPrefillData)
            }
          } else {
            canAccessPII map {
              case false => t.response.headOption.map(maskPiiForPiiAccessView)
              case true => t.response.headOption
            }
          }

        case Failure(e) =>
          logger.error("Error response while finding the transactions", e)
          Future.successful(None)
      }
    }.flatMap(identity)

    val sessionAccountId = userAuth.account.id
    val sessionUserId = userAuth.user.id
    val futureSubAccountDetails =
      if (userAuth.account.permission.contains(BusinessUserRoles.SAML_2_0.id) && !userAuth.account.permission.contains(BusinessUserRoles.NEW_SAML.id)) { // OLD SAML
        subAccountManagementClient.getSubAccounts(userAuth.account.id)
      } else if (userAuth.account.permission.contains(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)) {
        subAccountManagementClient.getSubAccountsV2(sessionAccountId, sessionUserId, sessionAccountId, None, None, true)
      } else {
        subAccountManagementClient.getSubAccounts(userAuth.account.id)
      }
    for {
      retrievedOptTrx <- futureTransaction
      listSubAccounts <- futureSubAccountDetails
      listPrograms <- getAllProgramIdsOfSpBank(userAuth.account.id, userAuth.user.id, userAuth.isProgram)
      responseTrxDetails <- processRetrievedTransaction(retrievedOptTrx, userAuth, listSubAccounts, listPrograms)
    } yield {
      responseTrxDetails match {
        case Left(a) => Left(a)
        case Right(b) =>
          val params = parse(b.base.requestProps.parameters.getOrElse[String]("{}")).extract[Map[String, Any]].filterKeys(key => key != "socurekey")
          Right(b.copy(base = b.base.copy(apiKey = None, requestProps = b.base.requestProps.copy(parameters = Some(Serialization.write(params))))))
      }
    }
  }

  private def maskPii(txn: ApiTransaction): ApiTransaction = {
    val maskedParams = txn.requestProps.parameters.map(p => ParamsMaskService.maskForObfuscateFields(p))
    val maskedCustomerUserId = txn.requestProps.customerUserId.map(p => transactionService.providedPlaceholder)
    val maskedResponse = txn.apiResponse.map(ResponseMaskService.mask).map(r=>maskDocumentTypeFromResponse(r))
    val requestProps = txn.requestProps.copy(parameters = maskedParams, customerUserId = maskedCustomerUserId)
    txn.copy(requestProps = requestProps, apiResponse = maskedResponse)
  }

  private def maskPiiForPiiAccessView(txn: ApiTransaction): ApiTransaction = {
    val maskedParams = txn.requestProps.parameters.map(p => ParamsMaskService.maskForObfuscateFields(p, true))
    val maskedResponse = txn.apiResponse.map(ResponseMaskService.mask).map(r=>maskDocumentTypeFromResponse(r,true))
    val requestProps = txn.requestProps.copy(parameters = maskedParams)
    txn.copy(requestProps = requestProps, apiResponse = maskedResponse)
  }

  private def maskDocumentTypeFromResponse(response: String, shouldExcludeFields: Boolean = false): String = {
      val parsedJson = parse(response)
      val maskedJson = parsedJson.transformField {
        case JField(COUNTRY, _) if (parsedJson \ DOCUMENT_VERIFICATION \ DOCUMENT_TYPE \ COUNTRY).isInstanceOf[JString] =>
          (COUNTRY, JString(transactionService.providedPlaceholder))
        case JField(STATE, _) if (parsedJson \ DOCUMENT_VERIFICATION \ DOCUMENT_TYPE \ STATE).isInstanceOf[JString] =>
          (STATE, JString(transactionService.providedPlaceholder))
        case JField(TYPE, _) if (!shouldExcludeFields && (parsedJson \ DOCUMENT_VERIFICATION \ DOCUMENT_TYPE \ TYPE).isInstanceOf[JString]) =>
          (TYPE, JString(transactionService.providedPlaceholder))
      }
    compact(render(maskedJson))
  }

  private def maskSSNFromRequestProps(txn: ApiTransaction): ApiTransaction = {
    if (txn.requestProps.parameters.isEmpty) {
      return txn
    }
    val parsedJson = parse(txn.requestProps.parameters.get)
    val maskedJson = parsedJson.transformField {
      case JField(NATIONAL_ID, _) if (parsedJson \ NATIONAL_ID).isInstanceOf[JString] =>
        (NATIONAL_ID, JString(transactionService.providedPlaceholder))
    }
    val parameters = compact(render(maskedJson))
    val requestProps = txn.requestProps.copy(parameters = Some(parameters))
    txn.copy(requestProps = requestProps)
  }

  private def maskSSNFromPrefillData(txn: ApiTransaction): ApiTransaction = {
    if (txn.apiResponse.isEmpty) {
      return txn
    }
    val parsedApiResponseJson = parse(txn.apiResponse.get)
    val maskedJson = parsedApiResponseJson.transformField {
      case JField(SSN_First_5, _) if (parsedApiResponseJson \ PREFILL \ SSN_First_5).isInstanceOf[JString] =>
        (SSN_First_5, JString(transactionService.providedPlaceholder))
    }
    val apiResponse = compact(render(maskedJson))
    txn.copy(apiResponse = Some(apiResponse))
  }

  private def searchTransaction(u: UserAuth,
                                account: Option[String],
                                start: DateTime,
                                end: DateTime,
                                env: Option[EnvironmentConstants],
                                pageNum: Int,
                                pageSize: Int,
                                msg: String): Future[Either[ErrorResponse, Seq[TransactionListItem]]] = {
    val creatorAccountId = u.account.id
    val creatorUserId = u.user.id
    if (u.account.permission.contains(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)) {
      val accountId = account match {
        case Some(x) => x.toLong
        case None => creatorAccountId
      }
      isValidAccountAccess(creatorAccountId, creatorUserId, accountId) flatMap {
        case Right(true) => env match {
          case Some(e) =>
            subAccountManagementClient.validateAccountAccess(e.id, creatorAccountId, creatorUserId, DashboardUserPermissions.TRANSACTIONS_VIEW.id.toString) flatMap {
              case Right(true) =>
                logger.info(s"Querying transactions $msg: $start - $end")
                execTransactionSearch(accountId, start, end, Set(e), pageNum, pageSize, msg)
              case Left(e) =>
                logger.error(s"Invalid account access for Account:$creatorAccountId, user:$creatorUserId", e)
                Future.successful(Left(e))
            }
          case None =>
            logger.error(s"Invalid environment provided for Account:$creatorAccountId, user:$creatorUserId")
            Future.successful(Left(ErrorMessage.InvalidEnvironment))
        }
        case Left(e) => Future.successful(Left(e))
      }
    } else {
      getAccountId(sessionAccountId = u.account.id, accountId = account) flatMap {
        case Right(x) =>
          logger.info(s"Querying transactions $msg: $start - $end")
          val envOpt = PermissionChecker.resolveEnvironment(u, DashboardUserRole.LIST_TRANSACTION, env)
          execTransactionSearch(x, start, end, envOpt, pageNum, pageSize, msg)
        case Left(error) => Future.failed(new Exception(error.message))
      }
    }
  }

  get("/by_date/:start/:end") {
    val startString = params("start")
    val endString = params("end")

    val environment = params.get("environment")
    val page = params.get("page")
    val size = params.get("size")
    val account = params.get("accountId")

    callAccountServiceWithSessionUser(u => {
      val start: DateTime = DateParser.startOfDay(DateParser.parseToUtcDateTime(startString))
      val end: DateTime = DateParser.endOfDay(DateParser.parseToUtcDateTime(endString))
      val env: Option[EnvironmentConstants] = environment.map(p ⇒ EnvironmentConstants.withName(p))
      val envOpt = PermissionChecker.resolveEnvironment(u, DashboardUserRole.LIST_TRANSACTION, env)
      val pageSize = if (size.isEmpty) DEFAULT_PAGE_SIZE else size.get.toInt
      val pageNum = if (page.isEmpty) DEFAULT_PAGE else page.get.toInt
      searchTransaction(u, account, start, end, env, pageNum, pageSize, "bydate.duration")
    })
  }

  get("/by/date/:start/:end") {
    val startString = params("start")
    val endString = params("end")


    val environment = params.get("environment")
    val page = params.get("page")
    val size = params.get("size")
    val account = params.get("accountId")

    callAccountServiceWithSessionUser(u => {
      val start: Long = startString.toLong
      val end: Long = endString.toLong

      val startDate: DateTime = DateParser.startOfDay(DateParser.unixToDateTime(start))
      val endDate: DateTime = DateParser.endOfDay(DateParser.unixToDateTime(end))

      val env: Option[EnvironmentConstants] = environment.map(p ⇒ EnvironmentConstants.withName(p))
      val pageSize = if (size.isEmpty) DEFAULT_PAGE_SIZE else size.get.toInt
      val pageNum = if (page.isEmpty) DEFAULT_PAGE else page.get.toInt
      searchTransaction(u, account, startDate, endDate, env, pageNum, pageSize, "bydate.duration")
    })
  }

  private def validateTransactionList(userAuth: UserAuth, apiTransaction: Seq[ApiTransaction], sessionAccountId: Long, sessionUserId: Long): Future[Seq[TransactionListItemExtd]] = {
    val res = apiTransaction.flatMap { at: ApiTransaction =>
      at.apiKey.map { ak =>
        val tmp = subAccountManagementClient.validateAccountAccess(ak, sessionAccountId, sessionUserId, DashboardUserPermissions.TRANSACTIONS_VIEW.id.toString, Some(ApiKeyStatus.values.map(_.id).toList.mkString(","))) map {
          case Right(true) =>
            val transaction: ApiTransaction = if (userAuth.account.permission.contains(BusinessUserRoles.MASK_PII_ON_VIEW.id)) maskPii(at) else at
            Some(TransactionListItemConverter.toTransactionListItemExtd(transaction))
          case Left(e) =>
            logger.error(s"Invalid account access for Account:$sessionAccountId, user:$sessionUserId, txnId: ${at.transactionId}", e)
            None
        }
        tmp.collect { case Some(listItem: TransactionListItemExtd) => listItem }
      }
    }
    Future.sequence(res)
  }

  private def validateTransactionListForProgram(userAuth: UserAuth, apiTransaction: Seq[ApiTransaction], programs: Set[Long]): Future[Seq[TransactionListItemExtd]] = {
    val res: Seq[Future[Option[TransactionListItemExtd]]] = apiTransaction.map { at =>
      val accountId = at.accountId.getOrElse(-1L)
      if (programs.contains(accountId)) {
        val transaction: ApiTransaction = if (userAuth.account.permission.contains(BusinessUserRoles.MASK_PII_ON_VIEW.id)) maskPii(at) else at
        Future.successful(Some(TransactionListItemConverter.toTransactionListItemExtd(transaction)))
      } else {
        logger.error(s"Invalid account access for Account:${userAuth.account.id}, user:${userAuth.user.id}, txnId: ${at.transactionId}")
        Future.successful(None)
      }

    }
    Future.sequence(res).map(_.flatten)
  }

  get("/by_id_or_cuid") {
    val id = params("id").trim
    val searchType = params.get("searchType").getOrElse(THIS_ACCOUNT)
    callAccountServiceWithSessionUser(u => {
      val sessionAccountId = u.account.id
      val sessionUserId = u.user.id

      def findTrxByIdOrCuid(accountIds: Set[Long]) = {
       transactionRestClient.findTrxByIdOrCuid(
          accountIds = accountIds,
          id = id,
          columns = TransactionListColumns,
          maskPii = true
        )
      }

      def findTrxByIdOrCuid0() = {
        if (searchType == ALL_PROGRAMS) {
          getAllProgramIdsOfSpBank(u.account.id, u.user.id, u.isProgram) flatMap {
            case Right(programIds) if programIds.nonEmpty =>
              processTrxListExt(u, findTrxByIdOrCuid(programIds))
            case Left(e) => Future.successful(Left(e))
          }
        } else if (searchType == ALL_SUB_ACCOUNT) {
          getAccountIds(sessionAccountId = u.account.id) flatMap { accountIds =>
            processTrxListExt(u, findTrxByIdOrCuid(accountIds))
          }
        } else {
          processTrxListExt(u, findTrxByIdOrCuid(Set(sessionAccountId)))
        }
      }

      if (u.account.permission.contains(BusinessUserRoles.SAML_2_0.id) && !u.account.permission.contains(BusinessUserRoles.NEW_SAML.id)) { // OLD SAML
        findTrxByIdOrCuid0()
      } else if (u.account.permission.contains(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)) {
        if (searchType == ALL_PROGRAMS) {
          getAllProgramIdsOfSpBank(u.account.id, u.user.id, u.isProgram) flatMap {
            case Right(programIds) =>
              findTrxByIdOrCuid(programIds) flatMap { p ⇒
                if (p.status == 200)
                  validateTransactionListForProgram(u, p.response, programIds).map(Right(_))
                else
                  Future.successful(Left(ErrorResponse(p.status, "Error processing search")))
              }
            case Left(e) => Future.successful(Left(e))
          }
        } else if (searchType == ALL_SUB_ACCOUNT) {
          subAccountManagementClient.getSubAccountsV2(sessionAccountId, sessionUserId, sessionAccountId, None, None, true) flatMap {
            case Right(list) =>
              val accountIds = list.map(_.id).toSet + sessionAccountId
              findTrxByIdOrCuid(accountIds) flatMap { p ⇒
                if (p.status == 200)
                  validateTransactionList(u, p.response, sessionAccountId, sessionUserId).map(Right(_))
                else
                  Future.successful(Left(ErrorResponse(p.status, "Error processing search")))
              }
            case Left(e) => Future.successful(Left(e))
          }
        } else {
          findTrxByIdOrCuid(Set(sessionAccountId)) flatMap { p ⇒
            if (p.status == 200)
              validateTransactionList(u, p.response, sessionAccountId, sessionUserId).map(Right(_))
            else
              Future.successful(Left(ErrorResponse(p.status, "Error processing search")))
          }
        }
      } else {
        findTrxByIdOrCuid0
      }
    })
  }

  get("/by_interval_days/:days") {
    val daysString = params("days")
    val environment = params.get("environment")
    val page = params.get("page")
    val size = params.get("size")
    val account = params.get("accountId")

    callAccountServiceWithSessionUser(u => {
      val now = clock.now()
      val days: Int = daysString.toInt
      val start = now.minusDays(days)
      val end = now

      val env: Option[EnvironmentConstants] = environment.map(p ⇒ EnvironmentConstants.withName(p))
      val pageSize = if (size.isEmpty) DEFAULT_PAGE_SIZE else size.get.toInt
      val pageNum = if (page.isEmpty) DEFAULT_PAGE else page.get.toInt
      searchTransaction(u, account, start, end, env, pageNum, pageSize, "byinterval")
    })
  }

  get("/by/interval/days/:days") {
    val daysString = params("days")
    val environment = params.get("environment")
    val page = params.get("page")
    val size = params.get("size")
    val account = params.get("accountId")

    callAccountServiceWithSessionUser(u => {
      val days: Int = daysString.toInt
      val endDate: DateTime = DateParser.getUTCTodayEndOfDay()
      val startDate: DateTime = DateParser.startOfDay(endDate.minusDays(days))
      val env: Option[EnvironmentConstants] = environment.map(p ⇒ EnvironmentConstants.withName(p))
      val pageSize = if (size.isEmpty) DEFAULT_PAGE_SIZE else size.get.toInt
      val pageNum = if (page.isEmpty) DEFAULT_PAGE else page.get.toInt
      searchTransaction(u, account, startDate, endDate, env, pageNum, pageSize, "byinterval")
    })
  }

  get("/modules") {
    callAccountServiceWithSessionUser(_ => {
      Future.successful(Right(IdPlusFeatures.getAll()))
    })
  }

  private def withBlacklistStatus(apiTransaction: ApiTransaction): Future[ApiTransaction] = {
    val result = blacklistService.getBlacklistInfo(apiTransaction.transactionId, apiTransaction.apiKey.get)
    result.map(p ⇒ if (p.status) {
      apiTransaction.copy(blacklistInfo = Some(p), isBlacklist = Some(true))
    } else apiTransaction)
  }

  private def isValidAccountAccess(sessionAccountId: Long, sessionUserId: Long, accountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    accountId match {
      case x if x != sessionAccountId =>
        subAccountManagementClient.getSubAccountsV2(sessionAccountId, sessionUserId, sessionAccountId, None, None, true).flatMap {
          case Right(l: List[SubAccountV2]) =>
            if (l.exists(_.id == accountId)) {
              Future.successful(Right(true))
            } else {
              Future.successful(Left(ErrorResponseFactory.get(AccessForbidden)))
            }
          case _ => Future.successful(Left(ErrorResponseFactory.get(SubAccountNotFound)))
        }
      case _ => Future.successful(Right(true))
    }
  }

  private def getAccountId(sessionAccountId: Long, accountId: Option[String]): Future[Either[ErrorResponse, Long]] = {
    accountId match {
      case Some(x) if x.toLong != sessionAccountId => subAccountManagementClient.getSubAccounts(sessionAccountId).flatMap {
        case Right(l: List[SubAccountV2]) => if (l.exists(_.id == accountId.get.toLong)) {
          Future.successful(Right(accountId.get.toLong))
        } else {
          Future.successful(Left(ErrorResponseFactory.get(SubAccountNotFound)))
        }
        case _ => Future.successful(Left(ErrorResponseFactory.get(SubAccountNotFound)))
      }
      case _ => Future.successful(Right(sessionAccountId))
    }
  }

  private def getAccountIds(sessionAccountId: Long): Future[Set[Long]] = {
    subAccountClient
      .findSubAccountsForAccountId(sessionAccountId)
      .map(_.map(_.id).toSet[Long])
  }

  private def isTrxFromParentOrSubAccount(sessionAccountId: Long,
                                          TrxAccountId: Option[Long],
                                          listSubAccounts: Either[ErrorResponse, List[SubAccountV2]]): Boolean = {
    (sessionAccountId == TrxAccountId.getOrElse(0), listSubAccounts.isRight) match {
      case (true, _) => true
      case (_, true) => listSubAccounts.right.get.exists { acctDetail =>
        acctDetail.id == TrxAccountId.getOrElse(0)
      }
      case (_, _) => false
    }
  }

  private def isTrxFromPrograms(listPrograms: Either[ErrorResponse, Set[Long]],
                                trxAccountId: Option[Long]): Boolean = {
    listPrograms match {
      case Right(programs) =>
        trxAccountId match {
          case Some(accountId) => programs.contains(accountId)
          case None => false
        }
      case Left(_) => false
    }
  }

  private def processRetrievedTransaction(retrievedOptTrx: Option[ApiTransaction],
                                          ua: UserAuth,
                                          listSubAccounts: Either[ErrorResponse, List[SubAccountV2]],
                                          listPrograms: Either[ErrorResponse, Set[Long]]):
  Future[Either[ErrorResponse, ExtendedTransactionDetails]] = {
    retrievedOptTrx match {
      case Some(transaction) =>
        if (!isTrxFromParentOrSubAccount(ua.account.id, transaction.accountId, listSubAccounts) && !isTrxFromPrograms(listPrograms, transaction.accountId)) {
          Future.successful(Left(ErrorResponseFactory.get(UnauthorizedTransactionId)))
        }
        else {
          val transactionEnvironment = transaction.environmentId.map(e => EnvironmentConstants(e.toInt))

          def extendedTransactionDetails0() = {
            if (PermissionChecker.resolveEnvironment(ua,
              DashboardUserRole.LIST_TRANSACTION,
              transactionEnvironment).nonEmpty) {
              extendedTransactionDetails(transaction)
            } else {
              Future.failed(AuthenticationException("No access allowed to this transaction"))
            }
          }

          if (ua.account.permission.contains(BusinessUserRoles.SAML_2_0.id) && !ua.account.permission.contains(BusinessUserRoles.NEW_SAML.id)) { // OLD SAML
            extendedTransactionDetails0()
          } else if (ua.account.permission.contains(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)) {
            transaction.apiKey match {
              case Some(apiKey) =>
                subAccountManagementClient.validateAccountAccess(apiKey, ua.account.id, ua.user.id, DashboardUserPermissions.TRANSACTIONS_VIEW.id.toString, Some(ApiKeyStatus.values.map(_.id).toList.mkString(","))) flatMap {
                  case Right(true) => extendedTransactionDetails(transaction)
                  case _ => getAllProgramIdsOfSpBank(ua.account.id, ua.user.id, ua.isProgram) flatMap {
                    case Right(programIds) if programIds.nonEmpty && programIds.contains(transaction.accountId.getOrElse(-1L)) =>
                      extendedTransactionDetails(transaction)
                    case Left(e) => Future.successful(Left(e))
                  }
                }
              case None =>
                Future.successful(Left(ErrorResponse(11, "Invalid Apikey in the transaction fetched")))
            }
          } else {
            extendedTransactionDetails0()
          }
        }
      case None =>
        Future.successful(Left(ErrorResponse(11, "No transactions match the search criteria")))
    }
  }

  private def extendedTransactionDetails(transaction: ApiTransaction): Future[Either[ErrorResponse, ExtendedTransactionDetails]] = {
    val transactionBase = withBlacklistStatus(transaction).map(q ⇒ Right(q)).recover {
      case t =>
        logger.error("could not query blacklist service", t)
        Right(transaction)
    }

    transactionBase.flatMap {
      case Right(trxBase) =>
        val a: Future[Either[ErrorResponse, ExtendedTransactionDetails]] = extendedTransactionDetailsExtractor.extract(trxBase).map { extendedTransactionDetails =>
          Right(extendedTransactionDetails.copy(
            base = removeDebug(extendedTransactionDetails.base)
          ))
        }
        a
      case _ => Future.successful(Left(ErrorResponse(1, "")))
    }
  }

  private def execTransactionSearch(accountId: Long,
                                    start: DateTime,
                                    end: DateTime,
                                    envOpt: Set[EnvironmentConstants.EnvironmentConstants],
                                    pageNum: Int,
                                    pageSize: Int,
                                    msg: String): Future[Either[ErrorResponse, Seq[TransactionListItem]]] = {

    val searchRequest = TransactionSearchRequest(
      accountId = Some(accountId),
      accountIds = Some(Set(accountId)),
      start = Some(start),
      end = Some(end),
      environment = Some(envOpt),
      pagination = Some(
        Pagination(
          page = pageNum,
          size = pageSize
        )
      ),
      product = None,
      isKyc = None,
      isError = None,
      customerUserId = None,
      runId = None,
      columns = TransactionListColumns,
      startTransactionId = None,
      sorting = Some(
        Seq(
          Sort(
            column = TransactionColumns.TRANSACTION_DATE,
            ascending = false
          )
        )
      ),
      maskPii = true
    )
    val retval = metrics.timeFuture(msg) {
      transactionRestClient.searchTransaction(searchRequest)
    }
    processTrxList(retval)
  }

  private def processTrxList(listFuture: Future[ApiTransactionResult]): Future[Either[ErrorResponse, Seq[TransactionListItem]]] = {
    listFuture map { p ⇒
      Try {
        if (p.status == 200) Right(p.response.map(TransactionListItemConverter.toListing))
        else Left(ErrorResponse(p.status, "Error processing search"))
      } match {
        case Success(res) => res
        case Failure(ex) =>
          logger.error("Failed to handle transaction result", ex)
          Left(ErrorResponse(InvalidInputFormat.id, "Transaction Not Found"))
      }
    }
  }

  private def processTrxListExt(userAuth: UserAuth, listFuture: Future[ApiTransactionResult]): Future[Either[ErrorResponse, Seq[TransactionListItemExtd]]] = {
    listFuture map { p ⇒
      Try {
        if (p.status == 200) {
          if (userAuth.account.permission.contains(BusinessUserRoles.MASK_PII_ON_VIEW.id)) Right(p.response.map(at => TransactionListItemConverter.toTransactionListItemExtd(maskPii(at))))
          else Right(p.response.map(at => TransactionListItemConverter.toTransactionListItemExtd(at)))
        } else {
          Left(ErrorResponse(p.status, "Error processing search"))
        }
      } match {
        case Success(res) => res
        case Failure(ex) =>
          logger.error("Failed to handle transaction result", ex)
          Left(ErrorResponse(InvalidInputFormat.id, "Transaction Not Found"))
      }
    }
  }

  private def removeDebug(transaction: ApiTransaction): ApiTransaction = {
    transaction.copy(scoringProps = transaction.scoringProps.copy(debug = None))
  }

  get("/watchlist/entities/:transaction_id") {
    val transactionId = params("transaction_id").trim
    callAccountServiceWithSessionUser(u => {
      val entityDetails = for {
        snapshotResponse <- transactionRestClient.findTransaction(transactionId, Set(TransactionColumns.API_RESPONSE), maskPii = true)
        entityCaseDetailsResponse <- txnCaseWorkflowServiceClient.getEntityInfoByTxnId(transactionId)
      } yield (entityCaseDetailsResponse, snapshotResponse)

      val entityDetailsResponse = entityDetails flatMap {

        case (Right(txnEntityInfo), apiTransactionResult) =>
          implicit val formats = DefaultFormats
          val snapshotViewEntityResponse: SnapshotViewEntityResponse = getEntityMatchesMap(apiTransactionResult.response.head.apiResponse.get)
          val caseDetails = WatchlistEntityCaseDetails(
            txnEntityInfo = Some(txnEntityInfo),
            snapshotViewEntityResponse = snapshotViewEntityResponse
          )
          Future.successful(Right(caseDetails))
        case (Left(entityInfoError), apiTransactionResult) =>
          logger.error(s"Error occurred while fetching entity disposition details for txnId ${transactionId}, error: ${entityInfoError.message}")
          val snapshotViewEntityResponse: SnapshotViewEntityResponse = getEntityMatchesMap(apiTransactionResult.response.head.apiResponse.get)
          val caseDetails = WatchlistEntityCaseDetails(
            txnEntityInfo = None,
            snapshotViewEntityResponse = snapshotViewEntityResponse
          )
          Future.successful(Right(caseDetails))
      }
      entityDetailsResponse
    })
  }

  post("/watchlist/translate") {
    Try(parsedBody.extract[WatchlistTranslateRequest]) match {
      case Success(watchlistTranslateRequest) =>
        callAccountServiceWithSessionUser(u => {
          val accountId = u.account.id
          val socureTranslatorHost = Try(config.getString("socure.translator.host")).getOrElse("")

          val defaultClient = TranslatorConfiguration.getDefaultApiClient.setVerifyingSsl(false).setBasePath(socureTranslatorHost)

          val apiInstance = new TranslatorDefaultApi(defaultClient)

          val translateRequest = new TranslateRequest()
          translateRequest.setToLanguage(watchlistTranslateRequest.toLanguage)
          translateRequest.setTransactionId(watchlistTranslateRequest.transactionId)
          translateRequest.setAccountId(accountId.toString)

          val originalTextsList = new util.ArrayList[OriginalTexts]()
          for(originalText <- watchlistTranslateRequest.originalTexts) {
            val javaOriginalText = new OriginalTexts()
            javaOriginalText.setId(originalText.id.orNull)
            javaOriginalText.setTexts(originalText.texts.asJava)
            originalTextsList.add(javaOriginalText)
          }
          translateRequest.setOriginalTexts(originalTextsList)

          try {
            val result: SuccessResponseWrapper = apiInstance.handleTranslateRequest(translateRequest)
            if(result.getStatus == TranslatorStatusEnum.OK) {
              val translateResponse: TranslateResponse = result.getData
              val watchlistTranslateResponse =
                WatchlistTranslateResponse(
                  accountId = translateResponse.getAccountId,
                  transactionId = translateResponse.getTransactionId,
                  translatedTexts =
                    translateResponse.getTranslatedTexts.asScala.map {
                      javaTranslatedText =>
                        TranslatedText(
                          id = Option(javaTranslatedText.getId),
                          texts = javaTranslatedText.getTexts.asScala.toList
                          )
                    }.toList
                  )
              Future.successful(Right(watchlistTranslateResponse))
            }
            else {
              logger.error(s"API error occurred when translating the provided texts. ApiResult: $result")
              Future.successful(Left(ErrorResponse(400, "Error in translating the texts provided!")))
            }
          }
          catch {
            case e: TranslatorApiException =>
              logger.error("ApiException occurred when translating the provided texts due to ", e)
              Future.successful(Left(ErrorResponse(400, "Error in translating the texts provided!")))
          }
        })
      case Failure(NonFatal(ex)) =>
        logger.error("Invalid request format", ex)
        Future.successful(BadRequest(body = "Invalid request format"))
    }
  }

  get("/watchlist/snapshot_view/:transaction_id") {
    val transactionId = params("transaction_id").trim
    callAccountServiceWithSessionUser(u => {

      entityMonitoringClient.getByTransactionId(transactionId) flatMap {
        case Right(entityResponse) =>
          val watchlistSearchDetailRequest: WatchlistSearchDetailRequest = getWatchlistSearchDetailRequest(entityResponse)
          val env: Option[EnvironmentConstants] = EnvironmentConstants.values.find(x => x.id == watchlistSearchDetailRequest.environmentId)
          val creatorAccountId = u.account.id
          val creatorUserId = u.user.id
          val accountId = watchlistSearchDetailRequest.accountId.toString

          def snapshotView0() = {
            val envOpt = PermissionChecker.resolveEnvironment(u, DashboardUserRole.LIST_TRANSACTION, env)
            envOpt.size match {
              case l if l > 0 =>
                getAccountId(sessionAccountId = u.account.id, accountId = Some(accountId)) flatMap {
                  case Right(_) =>
                    watchlistServiceClient.snapshotView(watchlistSearchDetailRequest, false)
                  case _ =>
                    logger.error("Problem fetching the account info from session")
                    Future.successful(Left(ErrorResponseFactory.get(SubAccountNotFound)))
                }
              case _ => Future.successful(Left(ErrorMessage.PermissionMissing))
            }
          }

          if (u.account.permission.contains(BusinessUserRoles.SAML_2_0.id) && !u.account.permission.contains(BusinessUserRoles.NEW_SAML.id)) { // OLD SAML
            snapshotView0()
          } else if (u.account.permission.contains(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)) {
            isValidAccountAccess(creatorAccountId, creatorUserId, accountId.toLong) flatMap {
              case Right(true) => env match {
                case Some(e) =>
                  subAccountManagementClient.validateAccountAccess(e.id, creatorAccountId, creatorUserId, DashboardUserPermissions.TRANSACTIONS_VIEW.id.toString) flatMap {
                    case Right(true) => watchlistServiceClient.snapshotView(watchlistSearchDetailRequest)
                    case Left(e) =>
                      logger.error(s"Invalid account access for Account:$creatorAccountId, user:$creatorUserId", e)
                      Future.successful(Left(e))
                  }
                case None =>
                  logger.error(s"Invalid environment provided for Account:$creatorAccountId, user:$creatorUserId")
                  Future.successful(Left(ErrorMessage.InvalidEnvironment))
              }
              case Left(e) => Future.successful(Left(e))
            }
          } else {
            snapshotView0()
          }
        case Left(e) =>
          Future.successful(Left(e))
      }
    })
  }

  get("/is_transaction_present/:environment_type_id") {
    val envTypeIdOpt = params.get("environment_type_id")
    val defaultDateFormat = DateTimeFormat.forPattern("yyyy-MM-dd")
    val now = clock.now()
    val startDate = now.minusYears(1).toString(defaultDateFormat)
    callAccountServiceWithSessionUser {
      userAuth =>
        val envTypeId = envTypeIdOpt.getOrElse(halt(400, "environment_type_id not provided"))
        val environment = EnvironmentConstants(envTypeId.toInt).toString
        transactionService.search(TransactionSearchInput(userAuth.account.id.toString, Some(environment), None, None, None, Some(startDate), None, None, Some(Set("decision")), None, Some(1), List.empty), userAuth.account.permission) flatMap {
          case Right(transactionList) =>
            Future.successful(Right(transactionList.nonEmpty))
          case Left(errorResponse: ErrorResponse) =>
            logger.error(s"Error while search transaction for account: ${userAuth.account.id}: ${errorResponse.message}")
            Future.successful(Left(errorResponse))

        }
    }
  }

  post("/otp/send") {
    Try(parsedBody.extract[OtpSendRequest]) match {
      case Success(otpSendInput) =>
        callAccountServiceWithSessionUser(u => {
          if(otpSendInput.environment == Sandbox_Name) {
            sandboxClient
              .createOtpTxn(otpSendInput, u.environment.find(_.name == otpSendInput.environment).get.socureKey)
              .map(r => Right(r))
          } else {
            idplusClient
              .createOtpTxn(otpSendInput, u.environment.find(_.name == otpSendInput.environment).get.socureKey)
              .map(r => Right(r))
          }
        })
      case Failure(NonFatal(ex)) =>
        logger.error("Invalid request format", ex)
        Future.successful(BadRequest(body = "Invalid request format"))
    }
  }

  post("/otp/verify") {
    Try(parsedBody.extract[OtpVerifyRequest]) match {
      case Success(otpVerifyRequest) =>
        callAccountServiceWithSessionUser(u => {
          if(otpVerifyRequest.environment == Sandbox_Name) {
            sandboxClient
              .verifyOtpTxn(otpVerifyRequest, u.environment.find(_.name == otpVerifyRequest.environment).get.socureKey)
              .map(r => Right(r))
          } else {
            idplusClient
              .verifyOtpTxn(otpVerifyRequest, u.environment.find(_.name == otpVerifyRequest.environment).get.socureKey)
              .map(r => Right(r))
          }
        })
      case Failure(NonFatal(ex)) =>
        logger.error("Invalid request format", ex)
        Future.successful(BadRequest(body = "Invalid request format"))
    }
  }

  post("/otp/list") {
    Try(parsedBody.extract[OtpSearchInput]) match {
      case Success(otpSearchInput) =>
        callAccountServiceWithSessionUser(u => {
          val mfaTranslatorHost = Try(config.getString("mfa.orchestrator.host")).getOrElse("")
          val defaultClient = MfaConfiguration.getDefaultApiClient.setVerifyingSsl(false).setBasePath(mfaTranslatorHost)
          val apiInstance = new MfaDefaultApi(defaultClient)
          val verificationsListRequest = new VerificationsListRequest()
          verificationsListRequest.setAccountId(u.account.id.toString)
          verificationsListRequest.setEnvironmentId(otpSearchInput.environmentType)
          verificationsListRequest.setPageSize(otpSearchInput.size)
          verificationsListRequest.setStartDate(otpSearchInput.startDate)
          verificationsListRequest.setEndDate(otpSearchInput.endDate)
          verificationsListRequest.setForward(otpSearchInput.isForward)
          if(otpSearchInput.pageToken.isDefined) {
            verificationsListRequest.setPageToken(otpSearchInput.pageToken.get)
          }
          try {
            val result = apiInstance.handleOTPVerificationListRequest(verificationsListRequest)
            if(result.getStatus == MfaStatusEnum.OK) {
              val response = OtpListVerificationsResponse(
                items = result.getData.getItems.asScala.map{
                  item => OtpVerification(
                    verificationId = item.getVerificationId,
                    verificationTime = item.getVerificationTime,
                    environmentIdAccountId = item.getEnvironmentIdAccountId,
                    destination = item.getDestination,
                    channel = item.getChannel,
                    operationType = item.getOperationType,
                    lastVerificationStatus = item.getLastVerificationStatus
                  )
                }.toList,
                count = result.getData.getCount,
                pageToken = result.getData.getPageToken,
                previousPageToken = result.getData.getPreviousPageToken
              )
              Future.successful(Right(response))
            } else {
              Future.successful(Left(ErrorResponse(400, result.getData().toString)))
            }
          } catch {
            case e: MfaApiException =>
              logger.error("ApiException occurred when fetching otp list due to ", e)
              Future.successful(Left(ErrorResponse(400, "Error in fetching otp list!")))
          }

        })
      case Failure(NonFatal(ex)) =>
        logger.error("Invalid request format", ex)
        Future.successful(BadRequest(body = "Invalid request format"))
    }
  }

  post("/otp/details") {
    Try(parsedBody.extract[OtpDetailInput]) match {
      case Success(otpSearchInput) =>
        callAccountServiceWithSessionUser(u => {
          val mfaTranslatorHost = Try(config.getString("mfa.orchestrator.host")).getOrElse("")
          val defaultClient = MfaConfiguration.getDefaultApiClient.setVerifyingSsl(false).setBasePath(mfaTranslatorHost)
          val apiInstance = new MfaDefaultApi(defaultClient)
          val verificationsListRequest = new VerificationEventsListRequest()
          verificationsListRequest.setAccountId(u.account.id.toString)
          verificationsListRequest.setEnvironmentId(otpSearchInput.environmentType)
          verificationsListRequest.setVerificationId(otpSearchInput.verificationId)
          try {
            val result = apiInstance.handleOTPVerificationEventsListRequest(verificationsListRequest)
            if(result.getStatus.toString == MfaStatusEnum.OK.toString) {
              val response = OtpDetailResponse(
                items = result.getData.getItems.asScala.map{
                  item => OtpDetail(
                    verificationEventTime = item.getVerificationEventTime,
                    verificationStatus = item.getVerificationStatus
                  )
                }.toList,
                count = result.getData.getCount,
                channel = result.getData.getChannel,
                destination = result.getData.getDestination
              )
              Future.successful(Right(response))
            } else {
              Future.successful(Left(ErrorResponse(400, result.getData().toString)))
            }
          } catch {
            case e: MfaApiException =>
              logger.error("ApiException occurred when otp details due to ", e)
              Future.successful(Left(ErrorResponse(400, "Error in fetching otp details!")))
          }

        })
      case Failure(NonFatal(ex)) =>
        logger.error("Invalid request format", ex)
        Future.successful(BadRequest(body = "Invalid request format"))
    }
  }



  private def getWatchlistSearchDetailRequest(entityResponse: EntityResponse): WatchlistSearchDetailRequest = {

    WatchlistSearchDetailRequest(entityResponse.searchId.toString, entityResponse.transactionId, entityResponse.accountId, entityResponse.environmentId,
      entityResponse.maskPII, entityResponse.dob, entityResponse.fuzzinessTolerance, entityResponse.dobAndName, entityResponse.dobTolerance, None, entityResponse.tier, None)

  }


  implicit class RichFuture[T](fut: Future[T]) {
    def mapAll[U](f: PartialFunction[Try[T], U]): Future[U] =
      fut.map(Success(_): Try[T]).recover {
        case t => Failure(t)
      }.map(f)
  }
}

object TransactionsServlet {
  val allowedServicesForPiiUnmasking = Set.apply[String]("kycPlus", "vitalRecords", "deceasedCheck")
}
