{
  account.service {
    #endpoint = "http://localhost:5000"
    endpoint="https://account-service.webapps.us-east-1.product-dev.socure.link"
    hmac {
      secret.key="""ENC(k6W6uTfQuybf9EeQwVi8NVv/TwOkZzv41C8EB6I1JlmH4QaF/JGCZqlo3422ML91eFdmL52zMR0bPGYuSU2bxA==)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }

  }
  idplus {
    endpoint = "https://idplus-service.webapps.us-east-1.product-dev.socure.link"
  }

  cookie {
    domain = "localhost"
  }

  sandbox {
    endpoint = "https://idplus-sandbox.webapps.us-east-1.product-dev.socure.link"
  }

  idm {
    user.management {
      endpoint = "https://ingress-private.us-east-1.pbls-stage.socure.link"
    }
  }

  executionContext {
    poolSize=100
  }

  domain {
    skip {
      secret = "ENC(juUdFVOpBzU8esb4EINfARiaXxkZXx5KKqFyFteIeXx3x1juywaf6GtR8qBNL4MZg8Kyae/f8Trtq52g+IF+Ww==)"
    }
  }

  memcache.servers="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link:11211"
  memcache.ttl.minutes = 1

  modelmanagement = {
    endpoint = "https://model-management.webapps.us-east-1.product-dev.socure.link"

    hmac {
      realm = "Socure"
      version = "1.0"
      strength = 512
      secret.refresh.interval= 5000
      aws.secrets.manager.id="model-management/dev/hmac-1a0b2a"
    }
  }

  modelManagementService = {
   endpoint = "https://model-management.webapps.us-east-1.product-dev.socure.link"

    hmac {
      realm = "Socure"
      version = "1.0"
      strength = 512
      secret.refresh.interval= 5000
      aws.secrets.manager.id="model-management/dev/hmac-1a0b2a"
    }
  }

  database {
    redshift {
      url = "jdbc:redshift://etl-v2-service-us-east-1-************-dev.colzpyfp6znj.us-east-1.redshift.amazonaws.com:5439/idplus_audit"
      username = "redshift_2020"
      password="""ENC(FDMh4JyStad6g8bKbjuHJ2ORBBS+UFNm5u9AaFYoKvtzMTmNTTWX12W6w+TgSsPD)"""
      driver="com.amazon.redshift.jdbc.Driver"
      dataSourceName="admin_dashboard_redshift"
    }
    c3p0 {
      initialPoolSize=10
      min_size=10
      max_size=20
    }
  }

  thread.count=10

  jetty {
    port = 5001
    threadPool {
      minSize=100
      maxSize=100
    }
    disable.https.cookies = false
    apiTimeout = "1800 seconds"
  }

  memcached {
    host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
    port=11211
  }

  mailgunConfig {
    host = "https://api.mailgun.net/v2/socure.com/messages"
    key = "ENC(gMdaz9ygW8R76o8U/jaqEGhHOwR1VyDzNbFH8pBQdn93ewiLGEcsOTmGNjcd9tchpzKJXYwdX6rth+tmo0VGIg==)"
    from = "Socure Support <<EMAIL>>"
    validationEndpoint = "https://api.mailgun.net/v4/address/validate"
  }

  aws.ses {
    region = "us-east-1"
    groupName = "UXFeatures"
    flagName = "Enable_Ses_Email_Service"
    assumeRoleArn = "arn:aws:iam::112942558241:role/product-dev-ses-role"
    assumeRoleRegion = "us-east-1"
  }

  notifications {
    sales = "<EMAIL>"
    tam = "<EMAIL>"
    support = "<EMAIL>"
    customersuccess = "<EMAIL>"
    infra = "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
    loginalert {
      to = "<EMAIL>"
      cc = []
      bcc = []
    }
  }

  sdk.mail {
    to = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    cc = ["<EMAIL>"]
  }

  dashboardv2config {
    url = "https://dashboard.dev.socure.com"
    resetpassword = "#!/reset_password_token"
    completeregister = "#!/activate"
    activatewithpassword = "#!/activate_with_password"
  }

  dashboardv3config {
    url = "https://dashboard-stage.socure.com"
    resetpassword = "#/reset_password_token"
    completeregister = "#/activate"
    activatewithpassword = "#/activate_with_password"
    decisionView = "/#/decision/configure/decision"
    passwordlesslogin = "/#/passwordless/login"
    decisionSponsorBankView = "/#/decision/configure/actions"
    productSettingActionView = "/#/actions/product_settings"
  }

  platformConfig {
    url = "http://localhost:9000",
    resetpassword = "login/reset_password",
    passwordlesslogin = "/login/passwordless/login",
    activatewithpassword = "login/set_password",
    completeregister = ""
  }

  reCaptcha{
      siteVerifyUrl = "https://recaptchaenterprise.googleapis.com/v1beta1/projects/socure-dashboardv2/assessments"
      privatekey="ENC(xNB4yCGDwmiiqO0oApzl31GUOBCok2R4ZhblOY5DNcPJftT9QPXwP6+vSiGXaiIld3HYU3e07J0xH9vMM+wAZw==)"
      apiKey="ENC(GdqwbCq3xEPB6zQUpjXTKSwTyBgqyNswG8Da450ZYZFTfqdy/ZwOhQ143P45R1ydqfYHcL6fQvk1KtyytvlGcQ==)"
  }

  cors{
    alloweddomains = ["https://dashboard.dev.socure.com", "http://localhost:9000", "http://localhost:3000", "http://localhost:2000", "https://dashboard-stage.socure.com", "https://demo.dev.socure.com", "https://developer.dev.socure.com", "https://riskos.poc.socure.com"]
  }

  sqs {
    sqsEndpoint= "https://sqs.us-east-1.amazonaws.com/"
    s3BucketName=action-auditing-dev-************-us-east-1
    queueName=action-auditing-dev
    region="us-east-1"
    # bathrun-stage
    kmsKey="ENC(5VTOvcojVlmkkG8PSTfLoAjrE0+qDFcOJnGp6LZtydT9T2np/q9QhBVV1v2TsCff0Hjc3IZH9zHI8sfbKKp/ev77nhE6rVvy84V4kye6spxEKy4KgFsq1FKZAXMJ58WU)"
    # stages3reader
  }

  jmx{
    port=1099
  }

  client.specific.encryption {
    encryption.context.account_id.key = "socure_account_id"
    kms.ids {

      "us-east-1" = """arn:aws:kms:us-east-1:************:alias/socure/client-specific-encryption-dev"""
      "us-west-1" = """arn:aws:kms:us-west-1:************:alias/socure/client-specific-encryption-dev"""
    }
  }

  document.verfication {
    uploadmaxsize = ********
    uploadrequestsize = ********
  }

  webhook.certificate {
    uploadmaxsize = 100000
    uploadrequestsize = 102400
  }

  max.post.request.size.bytes = ********

  saml2 {
    ssoRedirectDashboardPage = "https://dashboard.dev.socure.com/#!/saml2"
    ssoRedirectDevhubPage = "https://developer.dev.socure.com"
    ssoRedirectRiskOsPage = "https://riskos.dev.socure.com"
  }

  transaction.auditing {
    endpoint="https://transaction-auditing-service.webapps.us-east-1.product-dev.socure.link"

    dynamic.control.center{
      s3 {
        bucketName="globalconfig-************-us-east-1"
      }
      memcached {
        host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
        port=11211
        ttl=86400
      }
      local.cache {
        timeout.minutes=2
      }
  }

    hmac {
      secret.key="""ENC(MUPoZOTfN8uyYKkYUXihQIClptY0Sg10Orhr6kLPhyMbJEX9W0Om15pdwq2HZofa)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }
  }

   transaction-auditing {
      threadpool {
        poolSize=30
      }

      aws {

        maxRetries = 10

        primary {
          sqs {
            region=us-east-1
            transaction {
              queueName=transaction-auditing-stage
              waitTimeSeconds=20
              maxBatchSize=10
              maxBufferSize=60
            }
            third-party {
              queueName=third-party-transaction-auditing-stage
              waitTimeSeconds=20
              maxBatchSize=10
              maxBufferSize=60
            }
            producer {
              #maximum number of messages being processed by AmazonSQSAsync at the same time
              transaction {
                maxInFlight: 15
              }
              third-party {
                maxInFlight: 15
              }
            }
          }
        }

        fallback0 {
          sqs {
            region=us-west-2
            transaction {
              queueName=transaction-auditing-stage
              waitTimeSeconds=20
              maxBatchSize=10
              maxBufferSize=60
            }
            third-party {
              queueName=third-party-transaction-auditing-stage
              waitTimeSeconds=20
              maxBatchSize=10
              maxBufferSize=60
            }
            producer {
              #maximum number of messages being processed by AmazonSQSAsync at the same time
              transaction {
                maxInFlight: 15
              }
              third-party {
                maxInFlight: 15
              }
            }
          }
        }

        fallback1 {
          sqs {
            region=us-east-2
            transaction {
              queueName=transaction-auditing-stage
              waitTimeSeconds=20
              maxBatchSize=10
              maxBufferSize=60
            }
            third-party {
              queueName=third-party-transaction-auditing-stage
              waitTimeSeconds=20
              maxBatchSize=10
              maxBufferSize=60
            }
            producer {
              #maximum number of messages being processed by AmazonSQSAsync at the same time
              transaction {
                maxInFlight: 15
              }
              third-party {
                maxInFlight: 15
              }
            }
          }
        }

        s3 {
          largefiles {
            folder="sqs-storage-stage"
          }
          third-party {
            region=us-east-1
            bucket="thirdparty-stats-stage"
          }
        }

        sqs {
          backoff {
            # seconds
            min: 2
            max: 32
          }
        }
      }
    }

  file.storage.download {
    endpoint="https://file-storage-download.webapps.us-east-1.product-dev.socure.link"

    hmac {
      secret.key="""ENC(998DIPtcCTxkhnd6hg4CmhtzP7bRehCfeHxU66caDURqPHrrjYK9WP7l9MiH1snoDlXiOtdQS7G6/z7BODfNDA==)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }
    metrics.enabled = false
  }

  file.storage.upload.endpoint = "https://file-upload-service.webapps.us-east-1.product-dev.socure.link"
  file.storage.domain.skip.secret = """ENC(GLglUbNhIqXHTd334ti+4cBuqt07kTcGQXNVbIVLrnX+fYuBILb7lyADJLpeEgZPWOLmNH9Hf9AtTWiijYSRiA==)"""

  authentication.service {
    #endpoint ="http://localhost:5002"
    endpoint ="https://authentication.webapps.us-east-1.product-dev.socure.link"

    hmac {
      secret.key="""ENC(B/eaJYThj9xWf0jdOJhlZ/8E22gm367TH2AlqwI5eJZY04cIZCdL5NXHp5Oe4Yp/X3phJ5pFUpPWlmbRMB9MZA==)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }
  }

  reasoncode.service {
    endpoint="https://reasoncode-service.webapps.us-east-1.product-dev.socure.link"

    hmac {
      secret.key="""ENC(wh/Yg9OtarjI+KBRuY9GJTP5/7r7ir/f9TlhGO1FgzuLaIqVzSAnDzne2rzOuhTuQwiXshiTAXmjN45NYG+Pyw==)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }
    metrics.enabled = false
  }

  transaction.search {
      endpoint = "https://transaction-search-service.webapps.us-east-1.product-dev.socure.link"

      hmac {
          maxResultSize = 100
          realm = "PII"
          version = "1.0"
          strength = 512
          aws.secrets.manager.id = "transaction-search-service/dev/hmac/query-pii-3b4caf7f0d"
          secret.refresh.interval = 5000
      }
  }

  transaction.resolved.entity.service {
    endpoint = "https://transaction-resolved-entity-service.webapps.us-east-1.product-dev.socure.link/"

    hmac {
      realm = "Socure"
      version = "1.0"
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "transaction-resolved-entity-service/dev/hmac-5863caca"
      secret.refresh.interval = 5000
    }
  }

  #================ ThirdParty Reader Service config ================#
  thirdparty.reader.service {
    endpoint = "https://thirdparty-reader.webapps.us-east-1.product-dev.socure.link"

    hmac {
      realm="Socure"
      version="1.0"
      ttl=5
      time.interval=5
      strength=512
      aws.secrets.manager.id="thirdparty-reader-service/dev/hmac-5bbc4055"
      secret.refresh.interval=5000
    }
    metrics.enabled = false
  }
  #================ ThirdParty Reader Service config ================#

#================= Model Config V3 #=================

#Correlation Model - Name vs Address - US (V3.0)
datasci.model.defaults.v_3_0_0.cs_name_address.identifier="name_address_WP_HD_20190424_grid2_model_53"
datasci.model.defaults.v_3_0_0.cs_name_address.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_address.negative.threshold=0.20

#Correlation Model - Name vs Email - US (V3.0)
datasci.model.defaults.v_3_0_0.cs_name_email.identifier="name_email_30_corr_fuzzymatch_model_2"
datasci.model.defaults.v_3_0_0.cs_name_email.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_email.negative.threshold=0.20

#Correlation Model - Name vs Phone - US (V3.0)
datasci.model.defaults.v_3_0_0.cs_name_phone.identifier="name_phone_30_corr_fuzzymatch_model_5"
datasci.model.defaults.v_3_0_0.cs_name_phone.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_phone.negative.threshold=0.20

#Correlation Model - Name vs Address - International (=V3.0)
datasci.model.defaults.v_3_0_0.cs_name_address_int.identifier="name_address_WP_HD_20190424_grid2_model_53"
datasci.model.defaults.v_3_0_0.cs_name_address_int.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_address_int.negative.threshold=0.20

#Correlation Model - Name vs Email - International (=V3.0)
datasci.model.defaults.v_3_0_0.cs_name_email_int.identifier="name_email_30_corr_fuzzymatch_model_2"
datasci.model.defaults.v_3_0_0.cs_name_email_int.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_email_int.negative.threshold=0.20

#Correlation Model - Name vs Phone - International (=V3.0)
datasci.model.defaults.v_3_0_0.cs_name_phone_int.identifier="name_phone_30_corr_fuzzymatch_model_5"
datasci.model.defaults.v_3_0_0.cs_name_phone_int.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_phone_int.negative.threshold=0.20

#Address Risk US model
datasci.model.defaults.v_3_0_0.pii_risk_address_us.identifier="AddressRisk_GBM_WP_NS_FM_20190405"
datasci.model.defaults.v_3_0_0.pii_risk_address_us.low_risk.threshold=0.591204450670601
datasci.model.defaults.v_3_0_0.pii_risk_address_us.high_risk.threshold=0.760350271758428

#Email Risk US model
datasci.model.defaults.v_3_0_0.pii_risk_email_us.identifier="GBM_EmailRisk_WP_FC_NS_FM_2019_05_21"
datasci.model.defaults.v_3_0_0.pii_risk_email_us.low_risk.threshold=0.803054470577534
datasci.model.defaults.v_3_0_0.pii_risk_email_us.high_risk.threshold=0.939723723880854

#Phone Risk US model
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.identifier="phone_riskmodel_PF_WP_NS_20190412"
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.low_risk.threshold=0.834893108474672
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.high_risk.threshold=0.923285373677192

#Address Risk International model
datasci.model.defaults.v_3_0_0.pii_risk_address_int.identifier="AddressRisk_GBM_WP_NS_FM_20190405"
datasci.model.defaults.v_3_0_0.pii_risk_address_int.low_risk.threshold=0.801802614644041
datasci.model.defaults.v_3_0_0.pii_risk_address_int.high_risk.threshold=0.847327892956023

#Email Risk International model
datasci.model.defaults.v_3_0_0.pii_risk_email_int.identifier="GBM_EmailRisk_WP_FC_NS_FM_2019_05_21"
datasci.model.defaults.v_3_0_0.pii_risk_email_int.low_risk.threshold=0.787949596714919
datasci.model.defaults.v_3_0_0.pii_risk_email_int.high_risk.threshold=0.857516116761921

#Phone Risk International model
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.identifier="phone_riskmodel_PF_WP_NS_20190412"
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.low_risk.threshold=0.929784963657676
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.high_risk.threshold=0.97420772440818

#================= Model Config V3 #=================

  redirectURLs {
  		docs="https://developer.dev.socure.com/SetSID?session="
  		demo = "https://demo.dev.socure.com/SetSID?session="
  		platform = "http://localhost:8080/sid/set?session="
  	}
  sameSite="Strict"

  websdk.customization.uploadConfig {
    fileMaxSize=100000
    requestMaxSize=102400
  }
  entity.monitoring {
    endpoint="https://entity-monitoring-service.webapps.us-east-1.product-dev.socure.link"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "entity-monitoring-service/dev/hmac-0e1497"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
  }

  dynamic.control.center{
      s3 {
        bucketName="globalconfig-************-us-east-1"
      }
      memcached {
        host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
        port=11211
        ttl=86400
      }
      local.cache {
        timeout.minutes=2
      }
  }

  batch.job {
      endpoint="https://batch-job.webapps.us-east-1.product-dev.socure.link"

      hmac {
        ttl = 5
        time.interval = 5
        strength = 512
        aws.secrets.manager.id = "batch-job-service/dev/hmac-3dac6a7fca"
        secret.refresh.interval = 5000
        realm="Socure"
        version = "1.0"
      }
      aws {
          s3 {
             bucket.name = "batch-job-storage-dev-************-us-east-1"
             kms.key = """ENC(0zQOK7hq9vj9iEVKV/lRC5V2cnqxp+IppwgLF3Q/fLpKEhmV6n7r879d/gohM4/o4NhywKmiLkfDO+F7nquPTDWEj38PACZIxRirQFYipkDueF52FLoSIhsW1zR5R1ii)"""
          }
          region="us-east-1"
      }
      uploadMaxSize=52428800
      requestMaxSize=52428800
    }

  customer.customization {
    endpoint="https://customer-asset-storage.webapps.us-east-1.product-dev.socure.link"

    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "customer-asset-storage-service/dev/hmac-5830e86d"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
  }

  file.upload {
    bucket = "client-datafile-dev-************-us-east-1"
    kms_id = "arn:aws:kms:us-east-1:************:alias/socure/s3"
    uploadMaxSize=1073741824
    requestMaxSize=1078984704
  }

  badLoginConfig {
    maxTry=3
    atoLimit=10
  }

  document.manager {
    endpoint="https://document-manager.webapps.us-east-1.product-dev.socure.link"

    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id="document-manager/dev/hmac-835044"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
    memcached {
      host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
      port=11211
    }
  }

  stepUp.service {
    endpoint="https://step-up-service.webapps.us-east-1.product-dev.socure.link"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "step-up-service/dev/hmac-7c48d6"
      secret.refresh.interval = 5000
      realm = "Socure"
      version = "1.0"
    }
    metrics.enabled = false
  }

  device.risk {
    url="https://stage.dvnfo.com"
  }

  memcached.endpoint="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
  memcached.port=11211

  decision.service {
     endpoint = "https://decision-service.webapps.us-east-1.product-dev.socure.link"
     groupName="UXServicesRamp"
     flagName="DecisionService_Ramp"
     bccMails = ["<EMAIL>"]
   }


  globalwatchlist.service {
    endpoint = "https://watchlist-private-service.webapps.us-east-1.product-dev.socure.link/api"

    hmac {
      realm = "Socure"
      version = "1.0"
      ttl=5
      time.interval=5
      strength=512
      aws.secrets.manager.id="watchlist-private/dev/hmac-feec12"
      secret.refresh.interval=5000
    }
  }

  rate.limiter.token.bucket {
      redis.uri = "redis://dev-api-rate-limit.fxpzap.ng.0001.use1.cache.amazonaws.com"
      local.cache.max.size = 200 //used for maintaining list of policies
      cache.ttl = "1 minute"
      methods = ["POST","PUT","DELETE"]
      excludeUrls = ["/saml2"]
  }

  txn.case.workflow.service {
     endpoint = "https://txn-case-workflow-service.webapps.us-east-1.product-dev.socure.link"

     hmac {
       realm = "Socure"
       version = "1.0"
       ttl = 5
       time.interval = 5
       strength = 512
       aws.secrets.manager.id = "txn-case-workflow-service/dev/hmac-354cabcc"
       secret.refresh.interval = 5000
     }
   }

  watchlist.in.house {
    endpoint = "https://watchlist-in-house-ingestion.webapps.us-east-1.product-dev.socure.link"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      realm="Socure"
      version="1.0"
      aws.secrets.manager.id = "watchlist-in-house-ingestion/dev/hmac-6e570743"
      secret.refresh.interval = 5000
    }
  }

   txn.case.workflow.manager {
     endpoint = "https://txn-case-workflow-manager.webapps.us-east-1.product-dev.socure.link"

     hmac {
       realm = "Socure"
       version = "1.0"
       ttl = 5
       time.interval = 5
       strength = 512
       aws.secrets.manager.id = "txn-case-workflow-manager/dev/hmac-3ca32270"
       secret.refresh.interval = 5000
     }
   }

   entity.feedback.reader {
     endpoint = "https://entity-feedback-service.webapps.us-east-1.product-dev.socure.link"

     hmac {
       realm = "Socure"
       version = "1.0"
       ttl = 5
       time.interval = 5
       strength = 512
       aws.secrets.manager.id = "entity-feedback-service/dev/hmac-d8945679"
       secret.refresh.interval = 5000
     }
   }

   entity.feedback.worker {
     endpoint = "https://entity-feedback-worker.webapps.us-east-1.product-dev.socure.link"

     hmac {
       realm = "Socure"
       version = "1.0"
       ttl = 5
       time.interval = 5
       strength = 512
       aws.secrets.manager.id = "entity-feedback-worker/dev/hmac-4e6799d1"
       secret.refresh.interval = 5000
     }
   }
    custom.watchlist.manager {
           endpoint = "https://custom-wl-manager.webapps.us-east-1.product-dev.socure.link"

            hmac {
               realm = "Socure"
               version = "1.0"
               ttl=5
               time.interval=5
               strength=512
               aws.secrets.manager.id="custom-wl-manager/dev/hmac-596233"
               secret.refresh.interval=5000
             }
       }
       custom.watchlist.s3 {
             bucket.name="custom-wl-file-storage-************-us-east-1"
             kms.key="7be1b782-93b8-437d-81fb-da11d61e9af7"
             path {
               base_path="input"
             }
           }
   custom.watchlist.file.maxsize=200

  transaction.search {
    fields-and-boost {
      "parameters.pii.firstName" = 2.0,
      "parameters.pii.surName" = 2.0,
      "parameters.pii.city" = 1.0,
      "parameters.modules" = 1.0,
      "parameters.pii.email" = 4.0,
      "parameters.pii.email.domain" = 1.0,
      "parameters.pii.email.user" = 4.0,
      "parameters.pii.mobileNumber" = 4.0,
      "parameters.pii.mobileNumber.ten" = 4.0,
      "parameters.pii.nationalId" = 4.0,
      "parameters.pii.nationalId.four" = 2.0,
      "parameters.pii.zip" = 1.0,
      "parameters.pii.state" = 1.0,
      "parameters.pii.country" = 1.0,
      "parameters.pii.customerUserId" = 4.0,
      "parameters.pii.userId" = 4.0,
      "parameters.pii.firstName.partial" = 1.0,
      "parameters.pii.firstName.partial_extended" = 1.0,
      "parameters.pii.surName.partial" = 1.0,
      "parameters.pii.surName.partial_extended" = 1.0,
      "parameters.pii.physicalAddress.partial" = 1.0,
      "parameters.pii.physicalAddress2.partial" = 1.0,
      "parameters.pii.ipAddress" = 2.0,
      "parameters.pii.driverLicense" = 4.0,
      "parameters.documentUuid" = 4.0,
      "parameters.pii.companyName" = 1.0,
      "response.decisionResult" = 4.0,
      "response.reasonCodes" = 4.0,
      "parameters.businessName" = 1.0,
      "parameters.ein" = 2.0,
      "parameters.ein.digits" = 2.0,
      "parameters.payments.account.accountNumber" = 2.0,
      "parameters.payments.account.routingNumber" = 2.0,
      "parameters.businessPhone" = 1.0,
      "parameters.businessPhone.partial" = 1.0,
      "parameters.entityName" = 2.0
    }
    date.patterns = [
      "yyyy-MM-dd",
      "yyyy/MM/dd",
      "MM/dd/yyyy",
      "MM-dd-yyyy",
      "MMddyyyy",
      "yyyyMMdd"
    ]
  }


  #===================docv-berbix==========================#
  berbix.v1 {
   	endpoint="https://docv-bb-backend.webapps.us-east-1.product-dev.socure.link"
    mockHost = "https://socure-dv-mock-vendor-stage.socure.be/api/berbix/v1/"
    hmac {
        secret.key="""ENC(zaCYRQo7/8poI8la11/gU2DxmOzQJskcDs43UKO7cUmkNQyP1Zc1onobtlGT9rWRKWs26trw5t3Hpbc6BCvhw+q0+nIEAifiQnuuH1qhctQ=)"""
        strength=512
        realm="Socure"
        version="1.0"
    }
  }

  #============= DocV Orchestra ========#
  document.verification {
    docvOrchestra {
      endpoint = "https://document-orchestra.webapps.us-east-1.product-dev.socure.link/"
      endpoint2 = "https://document-orchestra.webapps.us-east-1.product-dev.socure.link/"
      hmac {
        realm="Socure"
        version = "1.0"
        strength=512
        aws.secrets.manager.id="docv-orchestra/dev/hmac"
      }
      metrics.enabled = false
      dynamic.control.center {
        s3 {
          bucketName = "globalconfig-************-us-east-1"
        }
        memcached {
          host=localhost
          port=11211
          ttl=86400
        }
        local {
          cache.timeout.minutes=2
        }
      }
    }
  }

  #============= TowerData =============#
  towerdata.endpoint="https://api.towerdata.com/v5/eppend"
  towerdata.apiKey="""ENC(NA3hHl+a7iCxMz5oJXs+icG18UX3U/dQaUnrgbTbelqYukKVoA0oxoT+/OIrEgTBI1bjwci+R8ta4Oam8Ap5UQ==)"""
  aws.quicksight {
      region = "us-east-1"
      awsaccountid = "************"
      session.minutes = 600,
      usernamespace = "default"
      dashboards {
        "overview" = "bb9855e3-7550-4f11-8a2b-bcb8fde11c75",
        "sponsorbankoverview" = "557bbdd4-24bc-48e8-81f8-14808730e0fc",
        "system" = "897a8f4e-7623-4abe-98ff-4d268fdc81b9",
        "reasoncodes" = "7dc808cf-4690-40b1-926b-9c74b7b51b64",
        "kyc" = "23642a94-ebda-4f99-9165-570a103674fe",
        "docv" = "af55d85a-0277-40db-a4ee-b5085992f55c",
        "decision" = "a9114faa-c4d4-4a14-aaf1-09f8d1ff06fa",
        "fraudscore" = "73fb5d7a-3395-444e-9b20-cc8d891a1041",
        "synthetic" = "a8f8be2c-8a3d-4ea9-9160-52ecc91cddb1",
        "emailrisk" = "213dcd29-d165-4c55-95e8-ee605c680582",
        "phonerisk" = "e109d214-64cf-45a6-b1e6-a478735d0d0e",
        "addressrisk" = "4e238851-37d6-4f64-94ad-d164d06e7803"
     }
  }

    #================ Rulecode Service config ================#
  rulecode.service {
   endpoint = "https://rulecode-service.webapps.us-east-1.product-dev.socure.link"

   hmac {
     realm="Socure"
     version="1.0"
     ttl=5
     time.interval=5
     strength=512
     aws.secrets.manager.id="rulecode-service/dev/hmac-49397d"
     secret.refresh.interval=5000
   }
 }
  batch.transaction {
   aws {
     s3 {
       bucket.name = "batch-data-processing-dev"
     }
   }
  }
  berbix.v1 {
    endpoint="https://docv-bb-backend.eks.us-east-1.product-stage.socure.link"
    hmac {
      secret.key="""ENC(NGeZZ7hL/fJ+JKrRGrc9mD9sNcQAGYDFZiGlJCpVXiMqWV+i7W1RgCxnnHQods01K03V822/IWhST9Hi/E8+dg==)"""
      strength=512
      realm="Socure"
      version="1.0"
    }
  }

  # watchlist case file upload limit.
  case.file.upload {
    uploadMaxSize = 20971520
    requestMaxSize = 26214400
  }

  negPos {
     url = "localhost"
     port = 6000
     useTLS = false
  }
  demoActivateUrl = "https://staging-socure-new.pantheonsite.io/request-demo"

  launchdarkly {
    sdk.key = "ENC(JqEGwWeO98sCr2BOogz1gl/FSKANcj4ZV0sOrCDAStZQzQqWr1VKO/R/7O897vGGPe/IVMwKpwPdEwSz4Ae1rQ==)"
    use.fedramp.version = false
  }

  # watchlist case closure
  watchlist.case.closure {
    bucket.name = "wl-case-mgmt-actions-dev-dbbdf432-us-east-1"
    base.path = "bulkCaseClosure/input/"
  }

  watchlist {
    audit {
      manager {
        max.retry.count = 3
        page.size = 100
        endpoint = "https://watchlist-audit-manager.webapps.us-east-1.product-dev.socure.link"
        hmac {
          realm="Socure"
          version="1.0"
          ttl=5
          time.interval=5
          strength=512
          aws.secrets.manager.id="watchlist-audit-manager/dev/hmac-76ad16ba"
          secret.refresh.interval=5000
        }
      }
    }
  }
  chatBot {
      endpoint = "https://agent-devhub.webapps.us-east-1.product-dev.socure.link"
      streamPath = "/chat/riskos/stream"
      feedbackPath = "/chat/%s/message/%s/feedback"
  }

    explainability {
      url = "explainability-service.webapps.us-east-1.product-dev.socure.link"
      port = 443
    }
  platformUrls = ["http://localhost:9000","https://riskos.dev.socure.com","https://riskos.poc.socure.com"]
  demoUrls = ["https://staging-socure-new.pantheonsite.io"]
  socure.translator {
    host = "https://socure-translator-service.webapps.us-east-1.product-dev.socure.link"
  }
}