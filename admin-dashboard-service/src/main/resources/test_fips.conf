{
  account.service {
    endpoint="https://account-service.webapps.us-east-1.product-dev.socure.link"
    endpoint2="https://account-service.webapps.us-east-1.product-dev.socure.link"
    groupName="UXServicesRamp"
    flagName="AccountService_Ramp"
    hmac {
      secret.key="""ENC(ID54T0RD/o8QiwFSTM++bxuNQYTsr9cmkd9rsxKbWklbvw/kyPkDhpi4/ESdseL3sb2Aw5yisXfjbLncJuV1vQ==)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }
  }

  idplus {
    endpoint = "https://idplus.webapps.us-east-1.product-dev.socure.link"
  }

  cookie {
    domain = "localhost"
  }

  sandbox {
    endpoint = "https://idplus-sandbox.webapps.us-east-1.product-dev.socure.link"
  }

  idm {
    user.management {
      endpoint = "https://ingress-private.us-east-1.pbls-stage.socure.link"
    }
  }

  executionContext {
    poolSize=100
  }

  domain {
    skip {
      secret = "ENC(zA45nQUp1jNsLJ5ZrFbju4YN71ZrDeXtGcApxBx2T8oS6RLeXlevVPJjvZiXAvzu4uWv+MYS6gST8TzzNIlbhFDNILk=)"
    }
  }

  memcache.servers="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link:11211"
  memcache.ttl.minutes = 1

  modelmanagement = {
    endpoint = "https://model-management.webapps.us-east-1.product-dev.socure.link"
    endpoint2 = "https://model-management.webapps.us-east-1.product-dev.socure.link"
    groupName="IdplusServicesRamp"
    flagName="ModelManagement_Ramp"
    hmac {
      realm = "Socure"
      version = "1.0"
      strength = 512
      secret.refresh.interval= 5000
      aws.secrets.manager.id="model-management/dev/hmac-1a0b2a"
    }
  }

  modelManagementService = {
    endpoint = "https://model-management.webapps.us-east-1.product-dev.socure.link"
    endpoint2 = "https://model-management.webapps.us-east-1.product-dev.socure.link"
    groupName="IdplusServicesRamp"
    flagName="ModelManagement_Ramp"
    hmac {
      realm = "Socure"
      version = "1.0"
      strength = 512
      secret.refresh.interval= 5000
      aws.secrets.manager.id="model-management/dev/hmac-1a0b2a"
    }
  }

  thread.count=10

  jetty {
    port = 5001
    threadPool {
      minSize=100
      maxSize=100
    }
    disable.https.cookies = false
    apiTimeout = "180 seconds"
  }

  memcached {
    host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
    port=11211
  }

  mailgunConfig {
    host = "https://api.mailgun.net/v2/socure.com/messages"
    key = "ENC(MNNTVqMg2NQot1joraOttE06NXSIrrbM6Hm6q/HnpSVEmJuZtPJqoi3RyvOALnIg6SmBiBevmqx0L1We0PAk3TUW+eg=)"
    from = "Socure Support <<EMAIL>>"
    validationEndpoint = "https://api.mailgun.net/v4/address/validate"
  }

  aws.ses {
    region = "us-east-1"
    assumeRoleArn = "arn:aws:iam::112942558241:role/product-dev-ses-role"
    assumeRoleRegion = "us-east-1"
    groupName = "UXFeatures"
    flagName = "Enable_Ses_Email_Service"
  }

  notifications {
    sales = "<EMAIL>"
    tam = "<EMAIL>"
    support = "<EMAIL>, <EMAIL>"
    customersuccess = "<EMAIL>"
    infra = "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
    loginalert {
      to = "<EMAIL>"
      cc = []
      bcc = []
    }
  }

  sdk.mail {
    to = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    cc = ["<EMAIL>"]
  }

  dashboardv2config {
    url = "https://dashboard.dev.socure.com"
    resetpassword = "#!/reset_password_token"
    completeregister = "#!/activate"
    activatewithpassword = "#!/activate_with_password"
  }

  dashboardv3config {
    url = "https://dashboard.dev.socure.com"
    resetpassword = "#/reset_password_token"
    completeregister = "#/activate"
    activatewithpassword = "#/activate_with_password"
    decisionView = "/#/decision/configure"
    passwordlesslogin = "/#/passwordless/login"
    decisionSponsorBankView = "/#/decision/configure/actions"
    productSettingActionView = "/#/actions/product_settings"
  }

  demoActivateUrl = "https://staging-socure-new.pantheonsite.io/request-demo"

  reCaptcha{
    siteVerifyUrl = "https://recaptchaenterprise.googleapis.com/v1beta1/projects/socure-dashboardv2/assessments"
    privatekey="ENC(g1CaXNr33JVvpWnu5HXwc0HFjh08bqFo7WpKKXRnfwQMJLP8HA+/zKyMiCX9WWtmHqZeD1sx81iRE9ILbqGob37SAU/M2p8f)"
    apiKey="ENC(86ieNSWNyjIXuUnuoaTWVx15p7lAeK0jSm2Rktj1K1pY3qsbw7LjDrgYr4+WVPygepr6BGqAyVe4DCU0VKF9fq2tkL60txo=)"
  }

  cors{
    alloweddomains = ["https://dashboard.dev.socure.com", "http://localhost:9000", "https://dashboard-stage.socure.com", "https://demo.dev.socure.com", "https://developer.dev.socure.com"]
  }

  sqs {
    sqsEndpoint= "https://sqs.us-east-1.amazonaws.com/"
    s3BucketName=action-auditing-dev-************-us-east-1
    queueName=action-auditing-dev
    region="us-east-1"
    # bathrun-stage
    kmsKey="ENC(MsqzM6jHWCzBeJ0NSqpI+uiOgtsQMPu8F6APNw9ekfq/HC3rMG406fxbhsBLqQgKg0bpo6O1zhQZNCnwrXP+sSLDMA0IrK9AR/ySLce5EcCBeBgM1sab0o2oCM+ftmXyQbCOENSZ08jTPqI=)"
    # stages3reader
  }

  jmx{
    port=1099
  }

  client.specific.encryption {
    encryption.context.account_id.key = "socure_account_id"
    kms.ids {

      "us-east-1" = """arn:aws:kms:us-east-1:************:alias/socure/client-specific-encryption-dev"""
      "us-west-1" = """arn:aws:kms:us-west-1:************:alias/socure/client-specific-encryption-dev"""
    }
  }

  document.verfication {
    uploadmaxsize = ********
    uploadrequestsize = ********
  }

  webhook.certificate {
    uploadmaxsize = 100000
    uploadrequestsize = 102400
  }

  max.post.request.size.bytes = ********

  saml2 {
    ssoRedirectDashboardPage = "https://dashboard.dev.socure.com/#!/saml2"
    ssoRedirectDevhubPage = "https://developer.dev.socure.com"
    ssoRedirectRiskOsPage = "https://riskos.dev.socure.com"
  }

  transaction.auditing {
    endpoint="https://transaction-auditing-service.webapps.us-east-1.product-dev.socure.link"
    endpoint2 = "https://transaction-auditing-service.webapps.us-east-1.product-dev.socure.link"

    dynamic.control.center{
      s3 {
        bucketName="globalconfig-************-us-east-1"
      }
      memcached {
        host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
        port=11211
        ttl=86400
      }
      local.cache {
        timeout.minutes=2
      }
    }

    hmac {
      secret.key="""ENC(c+1+o4kjkaJpKl2T9xVYWqoFdooOczAPjm1zWCbcG8apQu0AZmPJASbCa18JmynTWB+CXy3Fhfk=)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }
    metrics.enabled = false
  }

  transaction-auditing {
    threadpool {
      poolSize=30
    }

    aws {

      maxRetries = 10

      primary {
        sqs {
          region=us-east-1
          transaction {
            queueName=transaction-auditing-stage
            waitTimeSeconds=20
            maxBatchSize=10
            maxBufferSize=60
          }
          third-party {
            queueName=third-party-transaction-auditing-stage
            waitTimeSeconds=20
            maxBatchSize=10
            maxBufferSize=60
          }
          producer {
            #maximum number of messages being processed by AmazonSQSAsync at the same time
            transaction {
              maxInFlight: 15
            }
            third-party {
              maxInFlight: 15
            }
          }
        }
      }

      fallback0 {
        sqs {
          region=us-west-2
          transaction {
            queueName=transaction-auditing-stage
            waitTimeSeconds=20
            maxBatchSize=10
            maxBufferSize=60
          }
          third-party {
            queueName=third-party-transaction-auditing-stage
            waitTimeSeconds=20
            maxBatchSize=10
            maxBufferSize=60
          }
          producer {
            #maximum number of messages being processed by AmazonSQSAsync at the same time
            transaction {
              maxInFlight: 15
            }
            third-party {
              maxInFlight: 15
            }
          }
        }
      }

      fallback1 {
        sqs {
          region=us-east-2
          transaction {
            queueName=transaction-auditing-stage
            waitTimeSeconds=20
            maxBatchSize=10
            maxBufferSize=60
          }
          third-party {
            queueName=third-party-transaction-auditing-stage
            waitTimeSeconds=20
            maxBatchSize=10
            maxBufferSize=60
          }
          producer {
            #maximum number of messages being processed by AmazonSQSAsync at the same time
            transaction {
              maxInFlight: 15
            }
            third-party {
              maxInFlight: 15
            }
          }
        }
      }

      s3 {
        largefiles {
          folder="sqs-storage-stage"
        }
        third-party {
          region=us-east-1
          bucket="thirdparty-stats-stage"
        }
      }

      sqs {
        backoff {
          # seconds
          min: 2
          max: 32
        }
      }
    }
  }

  file.storage.download {
    endpoint="https://file-download.webapps.us-east-1.product-dev.socure.link"
    endpoint2="https://file-download.webapps.us-east-1.product-dev.socure.link"
    groupName="DVServicesRamp"
    flagName="FileDownload_Ramp"
    hmac {
      secret.key="""ENC(TC2JDqTLCIZXvPIU3U1rAuoCRFZ7N81sUEn83D7q86vdecKlN+fwnjgCZFZBBms1JR6AFPod6MeWci3S1S0PEg==)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }
    metrics.enabled = false
  }

  file.storage.upload.endpoint = "https://file-upload.webapps.us-east-1.product-dev.socure.link"
  file.storage.domain.skip.secret = """ENC(NlB1ad1nu+zOX6wRDc5nVhA3TwROYRnpR/h/3SIB8Kn+Wf2xsF3o2dd5pEufj+QbzybOdzINoOA3axlmqHwIiq7i8S8=)"""

  authentication.service {
    endpoint ="https://authentication.webapps.us-east-1.product-dev.socure.link"

    hmac {
      secret.key = """ENC(gzXOngvovl4Py9OlSHQw6pPNv92XcP78IEmv917KvB3DaT30f7FYlhqPHWVPvviYz66JVbo/jsGGl6mR1bsyDQ==)"""
      strength = 512
      realm = "Socure"
      version = "1.0"
    }
  }

  reasoncode.service {
    endpoint="https://reasoncode.webapps.us-east-1.product-dev.socure.link"
    endpoint2="https://reasoncode.webapps.us-east-1.product-dev.socure.link"
    groupName="IdplusServicesRamp"
    flagName="ReasonCodeService_Ramp"
    hmac {
      secret.key="""ENC(/vVYNcgK4bJk+Ulmo8bH/VCOWye8guTa5iUaQJSqqHezvhD5TFgjTCESCD3xN994Mj38JOOEfYWNGrK57t29pg==)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }
    metrics.enabled = false
  }

  transaction.search {
    endpoint = "https://transaction-search-service.webapps.us-east-1.product-dev.socure.link"
    endpoint2="https://transaction-search-service.webapps.us-east-1.product-dev.socure.link"
    groupName="UXServicesRamp"
    flagName="TransactionSearchService_Ramp"
    hmac {
      maxResultSize = 100
      realm = "PII"
      version = "1.0"
      strength = 512
      aws.secrets.manager.id = "transaction-search-service/dev/hmac/query-pii-3b4caf7f0d"
      secret.refresh.interval = 5000
    }
  }

  transaction.resolved.entity.service {
    endpoint = "https://transaction-resolved-entity-service.webapps.us-east-1.product-dev.socure.link/"
    endpoint2 = "https://transaction-resolved-entity-service.webapps.us-east-1.product-dev.socure.link/"
    groupName = "IdplusServicesRamp"
    flagName = "TransactionResolvedEntityService_Ramp"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "transaction-resolved-entity-service/dev/hmac-5863caca"
      secret.refresh.interval = 5000
    }
  }

  #================ ThirdParty Reader Service config ================#
  thirdparty.reader.service {
    endpoint = "https://thirdparty-reader.webapps.us-east-1.product-dev.socure.link"

    hmac {
      realm="Socure"
      version="1.0"
      ttl=5
      time.interval=5
      strength=512
      aws.secrets.manager.id="thirdparty-reader-service/dev/hmac-5bbc4055"
      secret.refresh.interval=5000
    }
    metrics.enabled = false
  }
  #================ ThirdParty Reader Service config ================#


  #================= Model Config V3 #=================

  #Correlation Model - Name vs Address - US (V3.0)
  datasci.model.defaults.v_3_0_0.cs_name_address.identifier="name_address_WP_HD_20190424_grid2_model_53"
  datasci.model.defaults.v_3_0_0.cs_name_address.positive.threshold=0.65
  datasci.model.defaults.v_3_0_0.cs_name_address.negative.threshold=0.20

  #Correlation Model - Name vs Email - US (V3.0)
  datasci.model.defaults.v_3_0_0.cs_name_email.identifier="name_email_30_corr_fuzzymatch_model_2"
  datasci.model.defaults.v_3_0_0.cs_name_email.positive.threshold=0.65
  datasci.model.defaults.v_3_0_0.cs_name_email.negative.threshold=0.20

  #Correlation Model - Name vs Phone - US (V3.0)
  datasci.model.defaults.v_3_0_0.cs_name_phone.identifier="name_phone_30_corr_fuzzymatch_model_5"
  datasci.model.defaults.v_3_0_0.cs_name_phone.positive.threshold=0.65
  datasci.model.defaults.v_3_0_0.cs_name_phone.negative.threshold=0.20

  #Correlation Model - Name vs Address - International (=V3.0)
  datasci.model.defaults.v_3_0_0.cs_name_address_int.identifier="name_address_WP_HD_20190424_grid2_model_53"
  datasci.model.defaults.v_3_0_0.cs_name_address_int.positive.threshold=0.65
  datasci.model.defaults.v_3_0_0.cs_name_address_int.negative.threshold=0.20

  #Correlation Model - Name vs Email - International (=V3.0)
  datasci.model.defaults.v_3_0_0.cs_name_email_int.identifier="name_email_30_corr_fuzzymatch_model_2"
  datasci.model.defaults.v_3_0_0.cs_name_email_int.positive.threshold=0.65
  datasci.model.defaults.v_3_0_0.cs_name_email_int.negative.threshold=0.20

  #Correlation Model - Name vs Phone - International (=V3.0)
  datasci.model.defaults.v_3_0_0.cs_name_phone_int.identifier="name_phone_30_corr_fuzzymatch_model_5"
  datasci.model.defaults.v_3_0_0.cs_name_phone_int.positive.threshold=0.65
  datasci.model.defaults.v_3_0_0.cs_name_phone_int.negative.threshold=0.20

  #Address Risk US model
  datasci.model.defaults.v_3_0_0.pii_risk_address_us.identifier="AddressRisk_GBM_WP_NS_FM_20190405"
  datasci.model.defaults.v_3_0_0.pii_risk_address_us.low_risk.threshold=0.591204450670601
  datasci.model.defaults.v_3_0_0.pii_risk_address_us.high_risk.threshold=0.760350271758428

  #Email Risk US model
  datasci.model.defaults.v_3_0_0.pii_risk_email_us.identifier="GBM_EmailRisk_WP_FC_NS_FM_2019_05_21"
  datasci.model.defaults.v_3_0_0.pii_risk_email_us.low_risk.threshold=0.803054470577534
  datasci.model.defaults.v_3_0_0.pii_risk_email_us.high_risk.threshold=0.939723723880854

  #Phone Risk US model
  datasci.model.defaults.v_3_0_0.pii_risk_phone_us.identifier="phone_riskmodel_PF_WP_NS_20190412"
  datasci.model.defaults.v_3_0_0.pii_risk_phone_us.low_risk.threshold=0.834893108474672
  datasci.model.defaults.v_3_0_0.pii_risk_phone_us.high_risk.threshold=0.923285373677192

  #Address Risk International model
  datasci.model.defaults.v_3_0_0.pii_risk_address_int.identifier="AddressRisk_GBM_WP_NS_FM_20190405"
  datasci.model.defaults.v_3_0_0.pii_risk_address_int.low_risk.threshold=0.801802614644041
  datasci.model.defaults.v_3_0_0.pii_risk_address_int.high_risk.threshold=0.847327892956023

  #Email Risk International model
  datasci.model.defaults.v_3_0_0.pii_risk_email_int.identifier="GBM_EmailRisk_WP_FC_NS_FM_2019_05_21"
  datasci.model.defaults.v_3_0_0.pii_risk_email_int.low_risk.threshold=0.787949596714919
  datasci.model.defaults.v_3_0_0.pii_risk_email_int.high_risk.threshold=0.857516116761921

  #Phone Risk International model
  datasci.model.defaults.v_3_0_0.pii_risk_phone_int.identifier="phone_riskmodel_PF_WP_NS_20190412"
  datasci.model.defaults.v_3_0_0.pii_risk_phone_int.low_risk.threshold=0.929784963657676
  datasci.model.defaults.v_3_0_0.pii_risk_phone_int.high_risk.threshold=0.97420772440818

  #================= Model Config V3 #=================

  redirectURLs {
    docs="https://developer.dev.socure.com/SetSID?session="
    demo = "https://demo.dev.socure.com/SetSID?session="
    platform = "http://localhost:8080/sid/set?session="
  }
  sameSite="None"

  websdk.customization.uploadConfig {
    fileMaxSize=100000
    requestMaxSize=102400
  }
  entity.monitoring {
    endpoint="https://entity-monitoring-service.webapps.us-east-1.product-dev.socure.link"
    endpoint2="https://entity-monitoring-service.webapps.us-east-1.product-dev.socure.link"
    groupName="IdplusServicesRamp"
    flagName="EntityMonitoringService_Ramp"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "entity-monitoring-service/dev/hmac-0e1497"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
  }

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-************-us-east-1"
    }
    memcached {
      host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  batch.job {
    endpoint="https://batch-job.webapps.us-east-1.product-dev.socure.link"
    endpoint2="https://batch-job.webapps.us-east-1.product-dev.socure.link"
    groupName="UXServicesRamp"
    flagName="BatchJobService_Ramp"

    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "batch-job-service/dev/hmac-3dac6a7fca"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
    aws {
      s3 {
        bucket.name = "batch-job-storage-dev-************-us-east-1"
        kms.key = """ENC(5cL+pLF2kvXvzfv3PWA9uz9qIaBTEi9o4ZVdlnXP5TEFN4fvQ0bv4H3pvYMzD4VE1E2xUmGEKmTdNcMmdg+zMMABkiw0cHIRoSJlkg0nY2CJunR1ISJEg5kX1Zh7MhyVzjyNCpIV5LnhbPY=)"""
      }
      region="us-east-1"
    }
    uploadMaxSize=52428800
    requestMaxSize=52428800
  }

  customer.customization {
    endpoint="https://customer-asset-storage.webapps.us-east-1.product-dev.socure.link"
    endpoint2="https://customer-asset-storage.webapps.us-east-1.product-dev.socure.link"
    groupName="DVServicesRamp"
    flagName="CustomerAssetStorage_Ramp"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "customer-asset-storage-service/dev/hmac-5830e86d"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
    metrics.enabled = false
  }

  file.upload {
    bucket = "client-datafile-dev-************-us-east-1"
    kms_id = "arn:aws:kms:us-east-1:************:alias/socure/s3"
    uploadMaxSize=1073741824
    requestMaxSize=1078984704
  }

  badLoginConfig {
    maxTry=3
    atoLimit=10
  }

  document.manager {
    endpoint="https://document-manager.webapps.us-east-1.product-dev.socure.link"
    endpoint2="https://document-manager.webapps.us-east-1.product-dev.socure.link"
    groupName="DVServicesRamp"
    flagName="DocumentManager_Ramp"

    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id="document-manager/dev/hmac-835044"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
    memcached {
      host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
      port=11211
    }
  }

  stepUp.service {
    endpoint="https://step-up-service.webapps.us-east-1.product-dev.socure.link"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "step-up-service/dev/hmac-7c48d6"
      secret.refresh.interval = 5000
      realm = "Socure"
      version = "1.0"
    }
    metrics.enabled = false
  }

  device.risk {
    url="https://stage.dvnfo.com"
  }

  memcached.endpoint="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
  memcached.port=11211

  decision.service {
    endpoint = "https://decision-service.webapps.us-east-1.product-dev.socure.link"
    endpoint2="https://decision-service.webapps.us-east-1.product-dev.socure.link"
    groupName="UXServicesRamp"
    flagName="DecisionService_Ramp"
    bccMails = ["<EMAIL>"]
  }

  globalwatchlist.service {
    endpoint = "https://watchlist-private-service.webapps.us-east-1.product-dev.socure.link/api"
    endpoint2="https://watchlist-private-service.webapps.us-east-1.product-dev.socure.link/api"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl=5
      time.interval=5
      strength=512
      aws.secrets.manager.id="watchlist-private/dev/hmac-feec12"
      secret.refresh.interval=5000
    }
  }

  rate.limiter.token.bucket {
    redis.uri = "redis://dev-api-rate-limit.fxpzap.ng.0001.use1.cache.amazonaws.com"
    local.cache.max.size = 200 //used for maintaining list of policies
    cache.ttl = "1 minute"
    methods = ["POST","PUT","DELETE"]
    excludeUrls = ["/saml2"]
  }
  txn.case.workflow.service {
    endpoint = "https://txn-case-workflow-service.webapps.us-east-1.product-dev.socure.link"
    endpoint2 = "https://txn-case-workflow-service.webapps.us-east-1.product-dev.socure.link"
    groupName="IdplusServicesRamp"
    flagName="TxnCaseWorkflowService_Ramp"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "txn-case-workflow-service/dev/hmac-354cabcc"
      secret.refresh.interval = 5000
    }
  }

  txn.case.workflow.manager {
    endpoint = "https://txn-case-workflow-manager.webapps.us-east-1.product-dev.socure.link"
    endpoint2 = "https://txn-case-workflow-manager.webapps.us-east-1.product-dev.socure.link"
    groupName="IdplusServicesRamp"
    flagName="TxnCaseWorkflowManager_Ramp"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "txn-case-workflow-manager/dev/hmac-3ca32270"
      secret.refresh.interval = 5000
    }
  }

  entity.feedback.reader {
    endpoint = "https://entity-feedback-service.webapps.us-east-1.product-dev.socure.link"
    endpoint2 = "https://entity-feedback-service.webapps.us-east-1.product-dev.socure.link"
    groupName="IdplusServicesRamp"
    flagName="EntityFeedbackService_Ramp"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "entity-feedback-service/dev/hmac-d8945679"
      secret.refresh.interval = 5000
    }
  }

  entity.feedback.worker {
    endpoint = "https://entity-feedback-worker.webapps.us-east-1.product-dev.socure.link"
    endpoint2 = "https://entity-feedback-worker.webapps.us-east-1.product-dev.socure.link"
    groupName="IdplusServicesRamp"
    flagName="EntityFeedbackWorker_Ramp"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "entity-feedback-worker/dev/hmac-4e6799d1"
      secret.refresh.interval = 5000
    }
  }
  custom.watchlist.manager {
    endpoint = "https://custom-wl-manager.webapps.us-east-1.product-dev.socure.link"
    endpoint2 = "https://custom-wl-manager.webapps.us-east-1.product-dev.socure.link"
    groupName="IdplusServicesRamp"
    flagName="CustomWLManager_Ramp"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl=5
      time.interval=5
      strength=512
      aws.secrets.manager.id="custom-wl-manager/dev/hmac-596233"
      secret.refresh.interval=5000
    }
  }
  custom.watchlist.s3 {
    bucket.name="custom-wl-file-storage-************-us-east-1"
    kms.key="7be1b782-93b8-437d-81fb-da11d61e9af7"
    path {
      base_path="input"
    }
  }
  custom.watchlist.file.maxsize=200

  transaction.search {
    fields-and-boost {
      "parameters.pii.firstName" = 5.0,
      "parameters.pii.surName" = 5.0,
      "parameters.pii.city" = 1.0,
      "parameters.modules" = 1.0,
      "parameters.pii.email" = 4.0,
      "parameters.pii.email.domain" = 1.0,
      "parameters.pii.email.user" = 4.0,
      "parameters.pii.mobileNumber" = 4.0,
      "parameters.pii.mobileNumber.ten" = 4.0,
      "parameters.pii.nationalId" = 4.0,
      "parameters.pii.nationalId.four" = 2.0,
      "parameters.pii.zip" = 1.0,
      "parameters.pii.state" = 1.0,
      "parameters.pii.country" = 1.0,
      "parameters.pii.customerUserId" = 4.0,
      "parameters.pii.userId" = 4.0,
      "parameters.pii.firstName.partial" = 1.0,
      "parameters.pii.firstName.partial_extended" = 1.0,
      "parameters.pii.surName.partial" = 1.0,
      "parameters.pii.surName.partial_extended" = 1.0,
      "parameters.pii.physicalAddress.partial" = 1.0,
      "parameters.pii.physicalAddress2.partial" = 1.0,
      "parameters.pii.ipAddress" = 2.0,
      "parameters.pii.driverLicense" = 4.0,
      "parameters.documentUuid" = 4.0,
      "parameters.pii.companyName" = 1.0,
      "response.decisionResult" = 4.0,
      "response.reasonCodes" = 4.0,
      "parameters.businessName" = 1.0,
      "parameters.ein" = 2.0,
      "parameters.ein.digits" = 2.0,
      "parameters.payments.account.accountNumber" = 2.0,
      "parameters.payments.account.routingNumber" = 2.0,
      "parameters.businessPhone" = 1.0,
      "parameters.businessPhone.partial" = 1.0,
      "parameters.entityName" = 2.0
    }
    date.patterns = [
      "yyyy-MM-dd",
      "yyyy/MM/dd",
      "MM/dd/yyyy",
      "MM-dd-yyyy",
      "MMddyyyy",
      "yyyyMMdd"
    ]
  }


  #===================docv-berbix==========================#
  berbix.v1 {
    endpoint="https://docv-bb-backend.webapps.us-east-1.product-dev.socure.link"
    mockHost = "https://socure-dv-mock-vendor-stage.socure.be/api/berbix/v1/"
    hmac {
      secret.key="""ENC(PyurXVZ0I+Z7eHianATx4fwqa4ztxNQNano15nBO1LtGJyUAmxUEMMhmqeqb+bRXoYRroDqdHLWj1JecfcfzKP0xQSekInf4nx2wUDlAcPB04eZOtkQ=)"""
      strength=512
      realm="Socure"
      version="1.0"
    }
  }

  #============= DocV Orchestra ========#
  document.verification {
    docvOrchestra {
      endpoint = "https://document-orchestra.webapps.us-east-1.product-dev.socure.link"
      endpoint2 = "https://document-orchestra.webapps.us-east-1.product-dev.socure.link"
      hmac {
        realm="Socure"
        version = "1.0"
        strength=512
        aws.secrets.manager.id="docv-orchestra/dev/hmac"
      }
      metrics.enabled = false
      dynamic.control.center {
        s3 {
          bucketName = "globalconfig-************-us-east-1"
        }
        memcached {
          host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
          port=11211
          ttl=86400
        }
        local {
          cache.timeout.minutes=2
        }
      }
    }
  }


  #============= TowerData =============#
  towerdata.endpoint="https://api.towerdata.com/v5/eppend"
  towerdata.apiKey="""ENC(FNFxz/Zp60zvhAvf2KkFSNR9vxPmvK/KTutjJ9nUh7wQR+fQPuFnYsS/0BZUTNQC2LKVI97PrE9lwGHbX3Ztbg==)"""
  aws.quicksight {
    region = "us-east-1"
    awsaccountid = "************"
    session.minutes = 600,
    usernamespace = "default"
    dashboards {
      "overview" = "bb9855e3-7550-4f11-8a2b-bcb8fde11c75",
      "sponsorbankoverview" = "557bbdd4-24bc-48e8-81f8-14808730e0fc",
      "system" = "897a8f4e-7623-4abe-98ff-4d268fdc81b9",
      "reasoncodes" = "7dc808cf-4690-40b1-926b-9c74b7b51b64",
      "kyc" = "23642a94-ebda-4f99-9165-570a103674fe",
      "docv" = "af55d85a-0277-40db-a4ee-b5085992f55c",
      "decision" = "a9114faa-c4d4-4a14-aaf1-09f8d1ff06fa",
      "fraudscore" = "73fb5d7a-3395-444e-9b20-cc8d891a1041",
      "synthetic" = "a8f8be2c-8a3d-4ea9-9160-52ecc91cddb1",
      "emailrisk" = "213dcd29-d165-4c55-95e8-ee605c680582",
      "phonerisk" = "e109d214-64cf-45a6-b1e6-a478735d0d0e",
      "addressrisk" = "4e238851-37d6-4f64-94ad-d164d06e7803"
    }
  }

  #================ Rulecode Service config ================#
  rulecode.service {
    endpoint = "https://rulecode-service.webapps.us-east-1.product-dev.socure.link"
    endpoint2 = "https://rulecode-service.webapps.us-east-1.product-dev.socure.link"
    groupName="IdplusServicesRamp"
    flagName="RulecodeService_Ramp"
    hmac {
      realm="Socure"
      version="1.0"
      ttl=5
      time.interval=5
      strength=512
      aws.secrets.manager.id="rulecode-service/dev/hmac-49397d"
      secret.refresh.interval=5000
    }
  }
  batch.transaction {
    aws {
      s3 {
        bucket.name = "batch-data-processing-dev"
      }
    }
  }

  # watchlist case file upload limit.
  case.file.upload {
    uploadMaxSize = 20971520
    requestMaxSize = 26214400
  }

  negPos {
    url = "label-list-service"
    port = 81
    useTLS = false
  }

  launchdarkly {
    sdk.key = "ENC(TrxFojosIw6l3A3Bt8udc4Hzbr0fCgLst7mxoN8FoisAdndsWNbuk4fbB998X787EvGdtAl7VMcHEl2diXMMYYIhubVwYmjK)"
    use.fedramp.version = false
  }

  # watchlist case closure
    watchlist.case.closure {
      bucket.name = "wl-case-mgmt-actions-dev-dbbdf432-us-east-1"
      base.path = "bulkCaseClosure/input/"
    }

    watchlist {
      audit {
        manager {
          max.retry.count = 3
          page.size = 100
          endpoint = "https://watchlist-audit-manager.webapps.us-east-1.product-dev.socure.link"
          hmac {
            realm="Socure"
            version="1.0"
            ttl=5
            time.interval=5
            strength=512
            aws.secrets.manager.id="watchlist-audit-manager/dev/hmac-76ad16ba"
            secret.refresh.interval=5000
          }
        }
      }
    }
     watchlist.in.house {
        endpoint = "https://watchlist-in-house-ingestion.webapps.us-east-1.product-dev.socure.link"
        hmac {
          ttl = 5
          time.interval = 5
          strength = 512
          realm="Socure"
          version="1.0"
          aws.secrets.manager.id = "watchlist-in-house-ingestion/dev/hmac-6e570743"
          secret.refresh.interval = 5000
        }
      }

      platformConfig {
          url = "http://localhost:9000",
          resetpassword = "login/reset_password",
          passwordlesslogin = "/login/passwordless/login",
          activatewithpassword = "login/set_password",
          completeregister = ""
      }

  chatBot {
    endpoint = "https://agent-devhub.webapps.us-east-1.product-dev.socure.link"
    streamPath = "/chat/riskos/stream"
    feedbackPath = "/chat/%s/message/%s/feedback"
  }
  explainability {
     url = "explainability-service.webapps.us-east-1.product-dev.socure.link"
     port = 443
  }
  socure.translator {
    host = "https://socure-translator-service.webapps.us-east-1.product-dev.socure.link"
  }
}
