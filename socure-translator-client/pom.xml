<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>me.socure</groupId>
    <artifactId>socure-translator-client</artifactId>
    <packaging>jar</packaging>
    <name>socure-translator-client</name>
    <version>${revision}</version>
    <description>OpenAPI Java client code</description>

    <parent>
        <groupId>me.socure</groupId>
        <artifactId>maven-root-java17</artifactId>
        <version>0.2-SNAPSHOT</version>
    </parent>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>properties-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>7.8.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <phase>generate-sources</phase>
                    </execution>
                </executions>
                <configuration>
                    <inputSpec>${project.basedir}/api-specs.yaml</inputSpec>
                    <generatorName>java</generatorName>
                    <output>${project.basedir}</output>
                    <apiPackage>me.socure.translator.client</apiPackage>
                    <modelPackage>me.socure.translator.client.model</modelPackage>
                    <groupId>me.socure</groupId>
                    <artifactId>socure-translator-client</artifactId>
                    <artifactVersion>${revision}</artifactVersion>
                    <configOptions>
                        <hideGenerationTimestamp>true</hideGenerationTimestamp>
                    </configOptions>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <!-- @Nullable annotation -->
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <version>3.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp-version}</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
            <version>${okhttp-version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson-version}</version>
        </dependency>
        <dependency>
            <groupId>io.gsonfire</groupId>
            <artifactId>gson-fire</artifactId>
            <version>${gson-fire-version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3-version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <version>${jakarta-annotation-version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
            <version>${jackson-databind-nullable-version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.ws.rs</groupId>
            <artifactId>jakarta.ws.rs-api</artifactId>
            <version>${jakarta.ws.rs-api-version}</version>
        </dependency>
        <!-- test dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit-version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-runner</artifactId>
            <version>${junit-platform-runner.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <gson-fire-version>1.9.0</gson-fire-version>
        <okhttp-version>4.12.0</okhttp-version>
        <gson-version>2.10.1</gson-version>
        <commons-lang3-version>3.14.0</commons-lang3-version>
        <jackson-databind-nullable-version>0.2.6</jackson-databind-nullable-version>
        <jakarta-annotation-version>1.3.5</jakarta-annotation-version>
        <junit-version>5.10.3</junit-version>
        <junit-platform-runner.version>1.10.0</junit-platform-runner.version>
        <jakarta.ws.rs-api-version>2.1.6</jakarta.ws.rs-api-version>
        <jsr311-api-version>1.1.1</jsr311-api-version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spotless.version>2.43.0</spotless.version>
    </properties>
</project>