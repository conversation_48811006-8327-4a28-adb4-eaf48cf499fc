openapi: 3.0.1
info:
  title: Socure Translator Service
  description: A service that translates texts from one language to other language
  version: "1.0"
servers:
- url: http://localhost:5000
  description: Generated server url
paths:
  /api/1.0/translate:
    post:
      summary: Translates Texts
      description: Translates texts from one language to another
      operationId: handleTranslateRequest
      requestBody:
        description: Translation request body
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TranslateRequest'
        required: true
      responses:
        "200":
          description: Translation is successfully completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponseWrapper'
        "400":
          description: Invalid request from the client
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponseWrapper'
components:
  schemas:
    OriginalTexts:
      required:
      - texts
      type: object
      properties:
        id:
          type: string
          description: Unique ID for the original texts (optional)
          example: "1938748"
        texts:
          type: array
          description: List of original texts to be translated
          example:
          - Este es un texto traducido en el idioma deseado
          - Este es el texto traducido correctamente
          items:
            type: string
            description: List of original texts to be translated
            example: "[\"Este es un texto traducido en el idioma deseado\",\"Este\
              \ es el texto traducido correctamente\"]"
      description: Represents the original texts for translation
    TranslateRequest:
      required:
      - accountId
      - originalTexts
      - toLanguage
      - transactionId
      type: object
      properties:
        toLanguage:
          type: string
          description: Target language code for translation
          example: en
        transactionId:
          type: string
          description: Transaction ID for which the translation is required
          example: f6ed9c72-a86b-4981-8cce-2d0d33e28e60
        originalTexts:
          type: array
          description: List of original texts with optional IDs to be translated
          items:
            $ref: '#/components/schemas/OriginalTexts'
        accountId:
          type: string
          description: Account ID for which the translation is required
          example: "4845"
      description: Represents a request to translate texts into another language
    SuccessResponseWrapper:
      required:
      - status
      type: object
      properties:
        status:
          type: string
          description: The status of the API response
          example: OK
          enum:
          - OK
          - ERROR
        data:
          $ref: '#/components/schemas/TranslateResponse'
    TranslateResponse:
      required:
      - translatedTexts
      type: object
      properties:
        translatedTexts:
          type: array
          description: A list of translated texts with optional IDs
          items:
            $ref: '#/components/schemas/TranslatedTexts'
      description: Represents the response containing a list of translated texts
    TranslatedTexts:
      required:
      - texts
      type: object
      properties:
        id:
          type: string
          description: Optional ID associated with the translated texts
          example: "1938748"
        texts:
          type: array
          description: List of translated texts
          example:
          - This is translated text in desired language
          - This is the correct translated text
          items:
            type: string
            description: List of translated texts
            example: "[\"This is translated text in desired language\",\"This is the\
              \ correct translated text\"]"
      description: Represents translated texts with an optional ID if given in input
    ErrorResponse:
      required:
      - code
      - message
      type: object
      properties:
        code:
          type: integer
          description: HTTP status code representing the error
          format: int32
          example: 400
        message:
          type: string
          description: Detailed error message explaining the reason for the error
          example: INVALID_ACCOUNT_ID
      description: Represents an error response with a status code and a message
    ErrorResponseWrapper:
      required:
      - status
      type: object
      properties:
        status:
          type: string
          description: The status of the API response
          example: OK
          enum:
          - OK
          - ERROR
        data:
          $ref: '#/components/schemas/ErrorResponse'
