<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>socure-translator-service</artifactId>
    <version>${revision}</version>
    <name>socure-translator-service</name>
    <description>Socure Translator Service</description>
    <modules>
        <module>socure-translator-service-app</module>
        <module>socure-translator-common</module>
        <module>socure-translator-service-core</module>
        <module>socure-translator-client</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <checkstyle.skip>true</checkstyle.skip>
        <java.version>17</java.version>
        <checkstyle.skip>true</checkstyle.skip>
        <socure.common.version>${revision}</socure.common.version>
        <lombok.version>1.18.30</lombok.version>
    </properties>

    <parent>
        <groupId>me.socure</groupId>
        <artifactId>maven-root-java17</artifactId>
        <version>0.2-SNAPSHOT</version>
    </parent>

    <packaging>pom</packaging>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <!-- Import dependency management from Spring Boot -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M3</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>properties-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>