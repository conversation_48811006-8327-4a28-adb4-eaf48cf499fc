package me.socure.translator;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {"me.socure", "software"})
@OpenAPIDefinition(info = @Info(title = "Socure Translator Service", version = "1.0", description = "A service that translates texts from one language to other language"))
public class SocureTranslatorServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(SocureTranslatorServiceApplication.class, args);
    }
}
