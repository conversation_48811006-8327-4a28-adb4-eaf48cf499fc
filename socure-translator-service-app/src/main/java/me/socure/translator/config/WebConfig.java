package me.socure.translator.config;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.log4j.Log4j2;
import me.socure.translator.common.model.request.TranslateRequest;
import me.socure.translator.common.model.response.ErrorResponseWrapper;
import me.socure.translator.common.model.response.SuccessResponseWrapper;
import me.socure.translator.handler.HealthCheckHandler;
import me.socure.translator.handler.TranslationRequestHandler;
import org.springdoc.core.annotations.RouterOperation;
import org.springframework.boot.autoconfigure.web.WebProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.config.WebFluxConfigurer;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.RouterFunctions;
import org.springframework.web.reactive.function.server.ServerResponse;

import static org.springframework.web.reactive.function.server.RequestPredicates.GET;
import static org.springframework.web.reactive.function.server.RequestPredicates.POST;

@Configuration
@Log4j2
public class WebConfig implements WebFluxConfigurer {

    @Bean
    public RouterFunction<ServerResponse> getHealthRouterFunction(HealthCheckHandler healthCheckHandler) {
        return RouterFunctions
                .route(GET("/healthcheck"), healthCheckHandler::handleHealthCheckRequest);
    }

    @Bean
    @RouterOperation(operation = @Operation(
            operationId = "handleTranslateRequest",
            summary = "Translates Texts",
            description = "Translates texts from one language to another",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Translation request body",
                    required = true,
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = TranslateRequest.class))
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Translation is successfully completed",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = SuccessResponseWrapper.class))),
                    @ApiResponse(responseCode = "400", description = "Invalid request from the client",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = ErrorResponseWrapper.class)))
            }
    ))
    public RouterFunction<ServerResponse> postTranslateRouterFunction(TranslationRequestHandler translationRequestHandler) {
        return RouterFunctions
                .route(POST("/api/1.0/translate"), translationRequestHandler::handleTranslateRequest);
    }

    @Bean
    public WebProperties.Resources resources() {
        return new WebProperties.Resources();
    }

}
