package me.socure.translator.processor;

import me.socure.translator.common.model.request.OriginalTexts;
import me.socure.translator.common.model.request.TranslateRequest;
import me.socure.translator.common.model.response.*;
import me.socure.translator.core.enums.ErrorMessage;
import me.socure.translator.core.model.TranslatedTextInfo;
import me.socure.translator.core.service.TranslationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;

import java.util.ArrayList;
import java.util.stream.Collectors;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
public class TranslationRequestProcessor {

    private final TranslationService translationService;

    @Autowired
    public TranslationRequestProcessor(TranslationService translationService) {
        this.translationService = translationService;
    }

    public Mono<ServerResponse> processTranslateRequest(final TranslateRequest translateRequest) {

        // Extract necessary data from the request
        String toLanguage = translateRequest.getToLanguage();
        List<OriginalTexts> originalTexts = translateRequest.getOriginalTexts();
        String accountId = translateRequest.getAccountId();
        String transactionId = translateRequest.getTransactionId();

        String errorMessage = "";
        if (!StringUtils.isNotEmpty(accountId)) {
            errorMessage = ErrorMessage.INVALID_ACCOUNT_ID.message;
        } else if (!StringUtils.isNotEmpty(transactionId)) {
            errorMessage = ErrorMessage.INVALID_TRANSACTION_ID.message;
        } else if (!StringUtils.isNotEmpty(toLanguage)) {
            errorMessage = ErrorMessage.INVALID_TO_LANGUAGE.message;
        } else if (originalTexts == null || originalTexts.isEmpty()) {
            errorMessage = ErrorMessage.INVALID_ORIGINAL_TEXTS.message;
        }
        if (StringUtils.isNotEmpty(errorMessage)) {
            return ServerResponse
                    .status(HttpStatus.BAD_REQUEST)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(new APIResponse<>(
                            APIResponse.Status.ERROR,
                            ErrorResponse.builder()
                                    .code(HttpStatus.BAD_REQUEST.value())
                                    .message(errorMessage)
                                    .build()));
        }

        return Flux.fromStream(originalTexts.stream())
                .parallel()
                .runOn(Schedulers.boundedElastic())
                .flatMap(entry -> {
                    String entityId = entry.getId();
                    List<String> textsToTranslate = entry.getTexts();

                    return translationService.translate(textsToTranslate, toLanguage, accountId, transactionId)
                            .flatMap(translationVendorResponse -> {
                                if (translationVendorResponse.getIsError()) {
                                    // Return an error if the translation fails
                                    return Mono.error(new RuntimeException(
                                            "Error during translation - "
                                                    + translationVendorResponse.getResponseStatus() + " - " + translationVendorResponse.getMessage()));
                                }

                                return Mono.just(TranslatedTexts.builder()
                                        .id(entityId)
                                        .texts(translationVendorResponse.getResponse()
                                                .stream()
                                                .map(TranslatedTextInfo::getTranslatedText)
                                                .collect(Collectors.toList()))
                                        .build());
                            });
                })
                .sequential()
                .collectList()
                .flatMap(translatedTexts -> {
                    // If no errors occurred, build and return the translateResponse
                    TranslateResponse translateResponse = TranslateResponse.builder()
                            .translatedTexts(translatedTexts)
                            .build();

                    return ServerResponse.ok()
                            .contentType(MediaType.APPLICATION_JSON)
                            .bodyValue(new APIResponse<>(APIResponse.Status.OK, translateResponse));
                })
                .onErrorResume(Exception.class, ex -> {
                    // Handle any errors that occur during processing
                    return ServerResponse.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .contentType(MediaType.APPLICATION_JSON)
                            .bodyValue(new APIResponse<>(
                                    APIResponse.Status.ERROR,
                                    ErrorResponse.builder()
                                            .code(HttpStatus.INTERNAL_SERVER_ERROR.value())
                                            .message(ex.getMessage())
                                            .build()));
                });
    }

    private HttpStatus mapStatusCode(int responseStatus) {
        if (responseStatus >= 400 && responseStatus < 500) {
            return HttpStatus.BAD_REQUEST;
        } else if (responseStatus >= 500 && responseStatus < 600) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        } else {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
    }
}
