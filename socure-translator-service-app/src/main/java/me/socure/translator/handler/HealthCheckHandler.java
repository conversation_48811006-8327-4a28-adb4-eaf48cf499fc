package me.socure.translator.handler;

import me.socure.translator.common.model.response.APIResponse;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;

import static org.springframework.web.reactive.function.server.ServerResponse.ok;

@Component
public class HealthCheckHandler {
    public Mono<ServerResponse> handleHealthCheckRequest(ServerRequest request) {
        return ok()
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(new APIResponse<>(APIResponse.Status.OK, null));
    }

}
