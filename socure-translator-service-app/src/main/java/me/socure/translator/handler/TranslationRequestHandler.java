package me.socure.translator.handler;

import me.socure.translator.common.model.request.TranslateRequest;
import me.socure.translator.processor.TranslationRequestProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;

@Component
public class TranslationRequestHandler {

    @Autowired
    TranslationRequestProcessor translationRequestProcessor;

    public Mono<ServerResponse> handleTranslateRequest(ServerRequest request) {
        return request.bodyToMono(TranslateRequest.class).flatMap(
                (final TranslateRequest translateRequest) -> translationRequestProcessor.processTranslateRequest(translateRequest)
        );
    }
}
