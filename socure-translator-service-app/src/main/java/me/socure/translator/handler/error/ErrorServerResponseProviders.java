package me.socure.translator.handler.error;

import lombok.extern.log4j.Log4j2;
import me.socure.translator.common.model.response.APIResponse;
import me.socure.translator.common.model.response.ErrorResponse;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.server.ServerResponse;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebInputException;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Provides server responses for various error classes.
 */

@Log4j2
enum ErrorServerResponseProviders {

    RESPONSE_STATUS_EXCEPTION(ResponseStatusException.class) {
        @Override
        public Mono<ServerResponse> getResponse(Throwable ex) {
            final HttpStatus status = (HttpStatus) ((ResponseStatusException) ex).getStatusCode();
            return ServerResponse
                    .status(status)
                    .bodyValue(
                            new APIResponse<>(
                                    APIResponse.Status.ERROR,
                                    ErrorResponse.builder()
                                            .code(status.value())
                                            .message(Optional.ofNullable(ex.getMessage()).orElse("ResponseStatusException"))
                                            .build()
                            )
                    );
        }
    },

    SERVER_WEB_INPUT_EXCEPTION(ServerWebInputException.class) {
        @Override
        public Mono<ServerResponse> getResponse(Throwable ex) {
            final HttpStatus status = HttpStatus.BAD_REQUEST;
            return ServerResponse
                    .status(status)
                    .bodyValue(
                            new APIResponse<>(
                                    APIResponse.Status.ERROR,
                                    ErrorResponse.builder()
                                            .code(status.value())
                                            .message(Optional.ofNullable(ex.getMessage()).orElse("ServerWebInputException"))
                                            .build()
                            )
                    );
        }
    },

    DEFAULT(Exception.class) {
        @Override
        public Mono<ServerResponse> getResponse(Throwable ex) {
            final HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
            return ServerResponse
                    .status(status)
                    .bodyValue(
                            new APIResponse<>(
                                    APIResponse.Status.ERROR,
                                    ErrorResponse.builder()
                                            .code(status.value())
                                            .message(Optional.ofNullable(ex.getMessage()).orElse("InternalServerException"))
                                            .build()
                            )
                    );
        }
    };

    private final Class<?> clazz;

    private ErrorServerResponseProviders(Class<?> clazz) {
        this.clazz = clazz;
    }

    public Class<?> getClazz() {
        return clazz;
    }

    public abstract Mono<ServerResponse> getResponse(Throwable ex);

    private static final Map<Class<?>, ErrorServerResponseProviders> REVERSE_MAPPING = new ConcurrentHashMap<>();

    static {
        for (ErrorServerResponseProviders provider : ErrorServerResponseProviders.values()) {
            REVERSE_MAPPING.put(provider.getClazz(), provider);
        }
    }

    public static Mono<ServerResponse> getServerResponse(Throwable ex) {
        log.error("Error while processing the request", ex);
        return REVERSE_MAPPING.getOrDefault(ex.getClass(), DEFAULT).getResponse(ex);
    }

}
