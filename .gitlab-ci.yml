include:
  - project: 'plt/gitlab-pipeline-templates'
    file: socure-services.gitlab-ci.yml

variables:
  PROJECT_NAME: idp
  SERVICE_NAME: socure-translator-service
  SERVICE_VERSION: 0.1.0
  DEV_ENVIRONMENT: "yes"
  MAVEN_EXTRA_ARGS: -Dbuild-environment=gitlab-ci -DskipTests -Dmaven.test.skip=true -Dscoverage.skip=true
  MAVEN_FIPS_ARGS: -Dbuild-environment=gitlab-ci-fips -DskipTests -Dmaven.test.skip=true -Dscoverage.skip=true
  MEND_PRODUCT_NAME: socure-saas
  JDK_VERSION: 17