package me.socure.translator.core.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder(toBuilder = true)
public class TranslationVendorResponse {
    private List<String> request;
    private List<TranslatedTextInfo> response;
    private String clientTraceId;
    private String url;
    private String vendorReferenceId;
    private Boolean isError;
    private Integer responseStatus;
    private String message;
}
