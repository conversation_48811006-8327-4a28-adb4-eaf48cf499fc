package me.socure.translator.core.config;

import com.typesafe.config.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class TranslatorConfiguration {
    @Bean
    public TranslatorConfigs getTranslatorConfigs(Config config) {
        String translationVendorName = config.getString("translator.vendor");
        return new TranslatorConfigs(translationVendorName, config.getInt(translationVendorName + ".tp.audit.service.id"), config.getInt(translationVendorName + ".charsLimit"), config.getInt(translationVendorName + ".textsLimit"), config.getStringList(translationVendorName + ".supportedLanguages"));
    }
}
