package me.socure.translator.core.util;

import org.apache.tika.language.detect.LanguageDetector;
import org.apache.tika.language.detect.LanguageResult;
import org.apache.tika.language.detect.LanguageWriter;

import java.util.Map;
import java.util.HashMap;

public class LanguageDetectorUtil {

    private static final Map<String, String> LANGUAGE_MAP = Map.ofEntries(
            Map.entry("zh-CN", "zh-Hans"),
            Map.entry("zh-TW", "zh-Hant"),
            Map.entry("be", "mn-Cyrl"),
            Map.entry("sr", "sr-Cyrl"),
            Map.entry("tl", "uz")
    );

    public static String detectLanguage(String text) {

        try{
            LanguageDetector detector = LanguageDetector.getDefaultLanguageDetector();
            detector.loadModels();
            LanguageWriter writer = new LanguageWriter(detector);
            writer.append(text);
            LanguageResult result = writer.getLanguage();
            writer.close();
            if(!result.isUnknown())
            {
                String lang =  result.getLanguage();
                for (String key : LANGUAGE_MAP.keySet()) {
                    if(key.equalsIgnoreCase(lang))
                        return LANGUAGE_MAP.get(key);
                }
                return lang;
            }
            else
                return "unknown";
        } catch (Exception e) {
            return "unknown";
        }
    }
}
