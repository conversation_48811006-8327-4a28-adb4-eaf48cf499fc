package me.socure.translator.core.microsoftai;

import com.typesafe.config.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
@Configuration
public class MicrosoftAIConfiguration {
    @Bean
    public MicrosoftAIConfigs getMicrosoftAIConfigs(Config config) {
        return new MicrosoftAIConfigs(config.getString("microsoftai.subscriptionKey"), config.getString("microsoftai.baseUrl"), config.getString("microsoftai.version"), config.getString("microsoftai.region"));
    }
}
