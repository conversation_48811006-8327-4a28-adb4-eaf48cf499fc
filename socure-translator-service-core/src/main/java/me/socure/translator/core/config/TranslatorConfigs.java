package me.socure.translator.core.config;
import java.util.List;

public class TranslatorConfigs {
    String translatorVendor;
    Integer tpAuditId;

    Integer charsLimit;

    Integer textsLimit;

    List<String> supportedLanguages;

    public TranslatorConfigs(String translatorVendor, Integer tpAuditId, Integer charsLimit, Integer textsLimit, List<String> supportedLanguages) {
        this.translatorVendor = translatorVendor;
        this.tpAuditId = tpAuditId;
        this.charsLimit = charsLimit;
        this.textsLimit = textsLimit;
        this.supportedLanguages = supportedLanguages;
    }

    public String getTranslatorVendor() {
        return translatorVendor;
    }

    public Integer getTpAuditId() {
        return tpAuditId;
    }

    public Integer getCharsLimit() {
        return charsLimit;
    }

    public Integer getTextsLimit() {
        return textsLimit;
    }

    public List<String> getSupportedLanguages() {
        return supportedLanguages;
    }
}
