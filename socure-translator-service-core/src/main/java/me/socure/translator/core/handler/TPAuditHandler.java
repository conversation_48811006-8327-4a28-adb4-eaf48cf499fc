package me.socure.translator.core.handler;

import me.socure.service.audit.client.TPAuditClient;
import me.socure.service.audit.model.AuditInfo$;
import me.socure.thirdparty.audit.common.ThirdPartyServiceIds;

import java.util.Date;

public class TPAuditHandler {

    private final TPAuditClient tpAuditClient;

    private final ThirdPartyServiceIds.EnumVal serviceId;

    public TPAuditHandler(TPAuditClient tpAuditClient, ThirdPartyServiceIds.EnumVal serviceId) {
        this.tpAuditClient = tpAuditClient;
        this.serviceId = serviceId;
    }

    public void audit(
            long accountId,
            String transactionId,
            Date startTime,
            String requestUrl,
            String requestBody,
            String responseBody,
            boolean isError,
            boolean maskPiiEnabled
    ) {
        tpAuditClient.audit(
                AuditInfo$.MODULE$.apply(
                        accountId,
                        transactionId,
                        startTime,
                        (new Date()).getTime() - startTime.getTime(),
                        false,
                        requestUrl,
                        requestBody,
                        responseBody,
                        isError,
                        null,
                        serviceId.name()
                ),
                maskPiiEnabled,
                transactionId
        );
    }
}
