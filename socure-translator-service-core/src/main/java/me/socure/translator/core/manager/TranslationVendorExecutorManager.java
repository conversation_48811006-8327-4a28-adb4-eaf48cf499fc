package me.socure.translator.core.manager;

import me.socure.translator.core.executor.MicrosoftAITranslationExecutor;
import me.socure.translator.core.model.TranslationVendorExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class TranslationVendorExecutorManager {
    private final Map<String, TranslationVendorExecutor> translationVendorExecutorMap = new ConcurrentHashMap<>();

    @Autowired
    public TranslationVendorExecutorManager(MicrosoftAITranslationExecutor microsoftAITranslationExecutor) {
        translationVendorExecutorMap.put("microsoftai", microsoftAITranslationExecutor);
    }

    public TranslationVendorExecutor getTranslationVendorExecutor(String vendorName) {
        return translationVendorExecutorMap.get(vendorName);
    }
}
