package me.socure.translator.core.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.typesafe.config.Config;
import me.socure.translator.core.config.TranslatorConfigs;
import me.socure.translator.core.manager.TranslationVendorExecutorManager;
import me.socure.translator.core.model.TranslatedTextInfo;
import me.socure.translator.core.model.TranslationVendorResponse;
import me.socure.translator.core.util.LanguageDetectorUtil;
import me.socure.translator.core.handler.TPAuditHandler;
import me.socure.service.audit.client.TPAuditClient;
import me.socure.thirdparty.audit.common.ThirdPartyServiceIds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class TranslationService {

    private final TranslationVendorExecutorManager translationVendorExecutorManager;
    private final Config config;

    private final TPAuditClient tpAuditClient;
    private final TranslatorConfigs translatorConfigs;

    private final ObjectMapper objectMapper;
    @Autowired
    public TranslationService(TranslationVendorExecutorManager translationVendorExecutorManager, Config config, TranslatorConfigs translatorConfigs, TPAuditClient tpAuditClient, ObjectMapper objectMapper) {
        this.translationVendorExecutorManager = translationVendorExecutorManager;
        this.config = config;
        this.translatorConfigs = translatorConfigs;
        this.tpAuditClient = tpAuditClient;
        this.objectMapper = objectMapper;
    }

    public Mono<TranslationVendorResponse> translate(List<String> originalTexts, String toLanguage, String accountId, String transactionId) {
        Map<Integer, String> nonMatchingTextsMap = new HashMap<>();
        List<TranslatedTextInfo> translatedTextInfos = new ArrayList<>(Collections.nCopies(originalTexts.size(), null));

        // Language detection
        for (int i = 0; i < originalTexts.size(); i++) {
            String text = originalTexts.get(i);
            String detectedLanguage = LanguageDetectorUtil.detectLanguage(text);

            if (detectedLanguage.equals(toLanguage)) {
                TranslatedTextInfo translatedText = TranslatedTextInfo.builder()
                        .fromLanguage(toLanguage)
                        .confidenceScore("1.0")
                        .translatedText(text).build();
                translatedTextInfos.set(i, translatedText);
            } else if(text.length() > translatorConfigs.getCharsLimit()){
                TranslatedTextInfo translatedText = TranslatedTextInfo.builder()
                        .fromLanguage(detectedLanguage)
                        .confidenceScore("0.0")
                        .translatedText("Original text exceeded the size limit for translation").build();
                translatedTextInfos.set(i, translatedText);
            }
            else if (detectedLanguage.equals("unknown") || isSupportedLanguage(detectedLanguage)) {
                nonMatchingTextsMap.put(i, text);
            } else {
                TranslatedTextInfo translatedText = TranslatedTextInfo.builder()
                        .fromLanguage(detectedLanguage)
                        .confidenceScore("0.0")
                        .translatedText("Language not supported for translation").build();
                translatedTextInfos.set(i, translatedText);
            }
        }

        if (nonMatchingTextsMap.isEmpty()) {
            return Mono.just(TranslationVendorResponse.builder()
                    .request(originalTexts)
                    .response(translatedTextInfos)
                    .clientTraceId(null)
                    .vendorReferenceId(null)
                    .url(null)
                    .isError(false)
                    .build());
        }

        // Batch the non-matching texts
        List<List<Map.Entry<Integer, String>>> batches = splitIntoBatches(new ArrayList<>(nonMatchingTextsMap.entrySet()));

        Date startDateTime = new Date();

        return Flux.fromStream(batches.stream())
                .parallel()
                .runOn(Schedulers.boundedElastic())
                .flatMap(batch -> {
                    List<String> textsToTranslate = batch.stream().map(Map.Entry::getValue).collect(Collectors.toList());
                    return translationVendorExecutorManager.getTranslationVendorExecutor(translatorConfigs.getTranslatorVendor())
                            .translate(textsToTranslate, toLanguage)
                            .flatMap(translationVendorResponse -> {
                                try {
                                    TPAuditHandler tpAuditHandler = new TPAuditHandler(tpAuditClient, ThirdPartyServiceIds.getByServiceId(translatorConfigs.getTpAuditId()));

                                    if (translationVendorResponse.getIsError()) {
                                        Map<String, String> response = new HashMap<>();
                                        response.put("status", String.valueOf(translationVendorResponse.getResponseStatus()));
                                        response.put("message", translationVendorResponse.getMessage());
                                        response.put("vendorReferenceId", translationVendorResponse.getVendorReferenceId());
                                        tpAuditHandler.audit(Long.parseLong(accountId), transactionId, startDateTime, translationVendorResponse.getUrl(), objectMapper.writeValueAsString(translationVendorResponse.getRequest()), objectMapper.writeValueAsString(response), true, false);
                                        return Mono.just(translationVendorResponse);
                                    }

                                    Map<String, String> response = new HashMap<>();
                                    response.put("vendorResponse", objectMapper.writeValueAsString(translationVendorResponse.getResponse()));
                                    response.put("vendorReferenceId", translationVendorResponse.getVendorReferenceId());
                                    tpAuditHandler.audit(Long.parseLong(accountId), transactionId, startDateTime, translationVendorResponse.getUrl(), objectMapper.writeValueAsString(translationVendorResponse.getRequest()), objectMapper.writeValueAsString(response), false, false);

                                    List<TranslatedTextInfo> nonMatchingTranslatedTexts = translationVendorResponse.getResponse();

                                    // Insert translated texts back at the correct original indexes using the map entries
                                    for (int i = 0; i < nonMatchingTranslatedTexts.size(); i++) {
                                        int originalIndex = batch.get(i).getKey(); // Get the original index from the map entry
                                        translatedTextInfos.set(originalIndex, nonMatchingTranslatedTexts.get(i)); // Set at correct index
                                    }

                                    return Mono.just(translationVendorResponse);
                                } catch (JsonProcessingException e) {
                                    return Mono.error(new RuntimeException("JSON processing error thrown", e));
                                }
                            })
                            .onErrorResume(e -> {
                                return Mono.error(new RuntimeException("Exception occurred in the translation service during translation with message" + e.getMessage()));
                            });
                })
                .sequential()
                .collectList()
                .flatMap(responses -> {
                    TranslationVendorResponse errorResponse = responses.stream()
                            .filter(TranslationVendorResponse::getIsError)
                            .findFirst()
                            .orElse(null);

                    if (errorResponse != null) {
                        return Mono.just(errorResponse);
                    } else {
                        return Mono.just(TranslationVendorResponse.builder()
                                .request(originalTexts)
                                .response(translatedTextInfos)
                                .clientTraceId(null)
                                .vendorReferenceId(null)
                                .url(null)
                                .isError(false)
                                .build());
                    }
                });
    }

    private List<List<Map.Entry<Integer, String>>> splitIntoBatches(List<Map.Entry<Integer, String>> entries) {
        List<List<Map.Entry<Integer, String>>> batches = new ArrayList<>();
        List<Map.Entry<Integer, String>> currentBatch = new ArrayList<>();
        int currentBatchCharCount = 0;

        for (Map.Entry<Integer, String> entry : entries) {
            String text = entry.getValue();  // Get the text from the map entry
            int textLength = text.length();

            // Check if the current batch can accommodate this text
            if (currentBatch.size() < translatorConfigs.getTextsLimit() && currentBatchCharCount + textLength <= translatorConfigs.getCharsLimit()) {
                currentBatch.add(entry);
                currentBatchCharCount += textLength;
            } else {
                // Add the current batch to batches and start a new batch
                batches.add(currentBatch);
                currentBatch = new ArrayList<>();
                currentBatch.add(entry);
                currentBatchCharCount = textLength;  // Reset character count with the current text length
            }
        }

        // Add any remaining batch if it's not empty
        if (!currentBatch.isEmpty()) {
            batches.add(currentBatch);
        }

        return batches;
    }

    private boolean isSupportedLanguage(String language){
        List<String> supportedLanguages = translatorConfigs.getSupportedLanguages();
        return supportedLanguages.stream().anyMatch(lang -> lang.equalsIgnoreCase(language));
    }
}
