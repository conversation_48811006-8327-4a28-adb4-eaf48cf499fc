package me.socure.translator.core.microsoftai;

import com.azure.ai.translation.text.TextTranslationAsyncClient;
import com.azure.ai.translation.text.TextTranslationClientBuilder;
import com.azure.ai.translation.text.TextTranslationServiceVersion;
import com.azure.core.credential.AzureKeyCredential;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MicrosoftAIAuthService {

    private final TextTranslationAsyncClient client;

    @Autowired
    public MicrosoftAIAuthService(MicrosoftAIConfigs microsoftAIConfigs) {
        AzureKeyCredential credential = new AzureKeyCredential(microsoftAIConfigs.getSubscriptionKey());
        TextTranslationServiceVersion serviceVersion = TextTranslationServiceVersion.V3_0;
        this.client = new TextTranslationClientBuilder()
                .region(microsoftAIConfigs.getRegion())
                .credential(credential)
                .serviceVersion(serviceVersion)
                .endpoint(microsoftAIConfigs.getBaseUrl())
                .buildAsyncClient();
    }

    public TextTranslationAsyncClient getMicrosoftAIClient() {
        return client;
    }
}
