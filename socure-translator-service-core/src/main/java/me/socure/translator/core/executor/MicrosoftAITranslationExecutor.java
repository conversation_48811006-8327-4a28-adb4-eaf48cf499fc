package me.socure.translator.core.executor;

import com.azure.ai.translation.text.models.InputTextItem;
import com.azure.core.exception.HttpResponseException;
import com.azure.core.http.HttpHeaderName;
import com.azure.core.http.rest.RequestOptions;
import com.azure.core.util.BinaryData;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.azure.ai.translation.text.models.DetectedLanguage;
import com.azure.ai.translation.text.models.TranslatedTextItem;
import com.azure.ai.translation.text.models.TranslationText;
import lombok.extern.log4j.Log4j2;
import me.socure.translator.core.microsoftai.MicrosoftAIConfigs;
import me.socure.translator.core.model.TranslatedTextInfo;
import me.socure.translator.core.model.TranslationVendorExecutor;
import me.socure.translator.core.model.TranslationVendorResponse;
import me.socure.translator.core.microsoftai.MicrosoftAIAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import me.socure.translator.core.util.UUIDUtil;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;

@Component
@Log4j2
public class MicrosoftAITranslationExecutor extends TranslationVendorExecutor {

    private final MicrosoftAIAuthService microsoftAIAuthService;

    private final ObjectMapper objectMapper;

    private final MicrosoftAIConfigs microsoftAIConfigs;


    @Autowired
    public MicrosoftAITranslationExecutor(MicrosoftAIAuthService microsoftAIAuthService, ObjectMapper objectMapper, MicrosoftAIConfigs microsoftAIConfigs) {
        this.microsoftAIAuthService = microsoftAIAuthService;
        this.objectMapper = objectMapper;
        this.microsoftAIConfigs = microsoftAIConfigs;
    }

    @Override
    public Mono<TranslationVendorResponse> translate(List<String> originalTexts, String toLanguage) {

        String baseUrl = microsoftAIConfigs.getBaseUrl();
        String clientTraceId = UUIDUtil.generateUUID();
        String url = baseUrl + "/translate?api-version=" + microsoftAIConfigs.getApiVersion() + "&to=" + toLanguage + "&clientTraceId=" + clientTraceId;

        List<String> toLanguages = new ArrayList<>();
        toLanguages.add(toLanguage);

        RequestOptions requestOptions = new RequestOptions();
        requestOptions.setHeader(HttpHeaderName.fromString("X-ClientTraceId"), clientTraceId);
        BinaryData body = convertTextToData(originalTexts);

        // Call translateWithResponse which returns Mono<Response<BinaryData>>
        return microsoftAIAuthService.getMicrosoftAIClient()
                .translateWithResponse(toLanguages, body, requestOptions)
                .flatMap(response -> {

                        try {
                            // Deserialize the BinaryData into your desired object
                            List<TranslatedTextItem> translations = objectMapper.readValue(response.getValue().toString(), new TypeReference<List<TranslatedTextItem>>() {});
                            List<TranslatedTextInfo> translatedTexts = new ArrayList<>();

                            for (TranslatedTextItem translation : translations) {
                                String fromLanguage = "", confidenceScore = "",  translatedString = "";
                                if (translation.getDetectedLanguage() != null) {
                                    DetectedLanguage detectedLanguage = translation.getDetectedLanguage();
                                    fromLanguage = detectedLanguage.getLanguage();
                                    confidenceScore = String.valueOf(detectedLanguage.getConfidence());
                                }
                                for (TranslationText textTranslation : translation.getTranslations()) {
                                    translatedString = textTranslation.getText();
                                }
                                TranslatedTextInfo translatedText = TranslatedTextInfo.builder()
                                        .fromLanguage(fromLanguage)
                                        .confidenceScore(confidenceScore)
                                        .translatedText(translatedString).build();
                                translatedTexts.add(translatedText);
                            }

                            TranslationVendorResponse translationVendorResponse = TranslationVendorResponse.builder()
                                    .request(originalTexts)
                                    .response(translatedTexts)
                                    .clientTraceId(clientTraceId)
                                    .vendorReferenceId(response.getHeaders().getValue(HttpHeaderName.fromString("X-requestid")))
                                    .url(url)
                                    .isError(false)
                                    .build();

                            return Mono.just(translationVendorResponse);
                        } catch (JsonProcessingException e) {
                            log.error("JsonProcessingException occurred while processing translation response with message " + e.getMessage());
                            return Mono.error(new RuntimeException("Error processing JSON response", e));
                        } catch (Exception e) {
                            log.error("Exception occurred during translation with message "+e.getMessage());
                            return Mono.error(new RuntimeException("Exception occurred during translation", e));
                        }
                })
                .onErrorResume(HttpResponseException.class,e -> {

                    TranslationVendorResponse translationVendorResponse = TranslationVendorResponse.builder()
                            .request(originalTexts)
                            .response(null)
                            .clientTraceId(clientTraceId)
                            .vendorReferenceId(e.getResponse().getHeaders().getValue(HttpHeaderName.fromString("X-requestid")))
                            .url(url)
                            .isError(true)
                            .responseStatus(e.getResponse().getStatusCode())
                            .message(e.getMessage())
                            .build();
                    return Mono.just(translationVendorResponse);
                })
                .onErrorResume(e -> {
                    log.error("Exception occurred during translation "+e.getMessage());
                    return Mono.error(new RuntimeException("Exception occurred during translation with message" + e.getMessage()));
                });
    }

    private BinaryData convertTextToData(List<String> texts) {
        List<InputTextItem> content = new ArrayList<>();
        for (String text : texts) {
            content.add(new InputTextItem(text));
        }
        BinaryData body = BinaryData.fromObject(content);
        return body;
    }
}
