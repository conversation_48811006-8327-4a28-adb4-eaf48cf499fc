translator.vendor=microsoftai

microsoftai {
  subscriptionKey = "ENC(TqhaU0PmyZe89Vs2RC0ERU/vv+7fRjGfNOHS1C5zLNohg6qE1LNEE4O35Vs68Jd8jjdDjVRVtVUZ20xFRWoBEg==)"
  region = "eastus"
  baseUrl = "https://api.cognitive.microsofttranslator.com"
  supportedLanguages = ["af", "sq", "am", "ar", "hy", "as", "az", "bn", "ba", "eu", "bho", "brx", "bs", "bg", "yue", "ca", "lzh", "zh-Hans", "zh-Hant", "sn", "hr", "cs", "da", "prs", "dv", "doi", "nl", "en", "et", "fo", "fj", "fil", "fi", "fr", "fr-ca", "gl", "ka", "de", "el", "gu", "ht", "ha", "he", "hi", "mww", "hu", "is", "ig", "id", "ikt", "iu", "iu-Latn", "ga", "it", "ja", "kn", "ks", "kk", "km", "rw", "tlh-Latn", "tlh-Piqd", "gom", "ko", "ku", "kmr", "ky", "lo", "lv", "lt", "ln", "dsb", "lug", "mk", "mai", "mg", "ms", "ml", "mt", "mi", "mr", "mn-Cyrl", "mn-Mong", "my", "ne", "nb", "nya", "or", "ps", "fa", "pl", "pt", "pt-pt", "pa", "otq", "ro", "run", "ru", "sm", "sr-Cyrl", "sr-Latn", "st", "nso", "tn", "sd", "si", "sk", "sl", "so", "es", "sw", "sv", "ty", "ta", "tt", "te", "th", "bo", "ti", "to", "tr", "tk", "uk", "hsb", "ur", "ug", "uz", "vi", "cy", "xh", "yo", "yua", "zu"]
  version = "3.0"
  charsLimit = 50000
  textsLimit = 1000
  tp.audit.service.id = 220
}

transaction-auditing {
  threadpool {
    poolSize = 30
  }

  aws {

    maxRetries = 10

    primary {
      sqs {
        region = us-east-2
        transaction {
          queueName = pltv2-ue2-transaction-auditing-prod
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        third-party {
          queueName = pltv2-ue2-third-party-transaction-auditing-prod
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region = us-west-2
        transaction {
          queueName = pltv2-ue2-transaction-auditing-prod
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        third-party {
          queueName = pltv2-ue2-third-party-transaction-auditing-prod
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region = us-east-1
        transaction {
          queueName = pltv2-ue2-transaction-auditing-prod
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        third-party {
          queueName = pltv2-ue2-third-party-transaction-auditing-prod
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder = "sqs-storage-prod-112942558241-us-east-2"
      }
      third-party {
        region = us-east-2
        bucket = "thirdparty-stats-prod-112942558241-us-east-2"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}