  {
  account.service {
    endpoint="http://account-service"
    endpoint2="http://account-service"
    groupName="UXServicesRamp"
    flagName="AccountService_Ramp"
    hmac {
      secret.key="""ENC(UwM24GbKEVTORnjLXJLr9a3d/4PKe83Yc3P+OKzo9EyC8Psdr0sBDR7aU3AnYiF8CxkjSa+vFYx48jjqmURdGw==)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }
  }
  idplus {
    endpoint = "https://service.socure.com"
  }

  cookie {
    domain = "api-dashboardv2.prod.socure.com"
  }

  sandbox {
    endpoint = "https://sandbox.socure.com"
  }

  executionContext {
    poolSize=100
  }

  domain {
    skip {
      secret = "ENC(FAlpWwhjLlEmtJqI1ddy1jc/1QcbAW6MYJxch2h6SuZpVl6lq9Au9ZDEvBsvjcWWPtMP3mNM/362VmAij9D5V7NOaqo=)"
    }
  }

  idm {
    user.management {
      endpoint = "https://ingress-private.us-east-1.pbls-prod.socure.link"
    }
  }

  memcache.servers="vpc-memcached-prod-2020.ps2mlp.cfg.use1.cache.amazonaws.com:11211"
  memcache.ttl.minutes = 1

  modelmanagement = {
    endpoint = "http://model-management"
    endpoint2 = "http://model-management"

    hmac {
      realm = "Socure"
      version = "1.0"
      strength = 512
      secret.refresh.interval= 5000
      aws.secrets.manager.id="model-management/prod/hmac"
    }
  }

  thread.count=10

  jetty {
    threadPool {
      minSize=100
      maxSize=100
    }
    disable.https.cookies = false
    apiTimeout = "180 seconds"
  }

  memcached {
    host=vpc-memcached-prod-2020.ps2mlp.cfg.use1.cache.amazonaws.com
    port=11211
  }

  mailgunConfig {
    host = "https://api.mailgun.net/v2/socure.com/messages"
    key = "ENC(/z0+iWgGaHlzyL7UwdHZADpOEV524nJJyR7AiR6k9VRU74itrGID5BUWGwNjuxn7zBBDGHeVl1uYhsLtO1aKdLODBic=)"
    from = "Socure Support <<EMAIL>>"
    validationEndpoint = "https://api.mailgun.net/v4/address/validate"
  }

  aws.ses {
    region = "us-east-1"

    groupName = "UXFeatures"
    flagName = "Enable_Ses_Email_Service"
  }

  sdk.mail {
    to = ["<EMAIL>"]
    cc = ["<EMAIL>", "<EMAIL>"]
  }

  notifications {
    sales = "<EMAIL>"
    support = "<EMAIL>"
    tam = "<EMAIL>, <EMAIL>"
    customersuccess = "<EMAIL>"
    infra = "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
    loginalert {
      to = "<EMAIL>"
      cc = ["<EMAIL>","<EMAIL>"]
      bcc = ["<EMAIL>"]
    }
  }

  dashboardv2config {
    url = "https://admin.socure.com"
    resetpassword = "#!/reset_password_token"
    completeregister = "#!/activate"
    activatewithpassword = "#!/activate_with_password"
  }

  dashboardv3config {
      url = "https://dashboard.socure.com"
      resetpassword = "#/reset_password_token"
      completeregister = "#/activate"
      activatewithpassword = "#/activate_with_password"
      decisionView = "/#/decision/configure"
      passwordlesslogin = "/#/passwordless/login"
      decisionSponsorBankView = "/#/decision/configure/actions"
      productSettingActionView = "/#/actions/product_settings"
  }

  platformConfig {
      url = "https://riskos.socure.com",
      resetpassword = "login/reset_password",
      passwordlesslogin = "/login/passwordless/login",
      activatewithpassword = "login/set_password",
      completeregister = ""
  }

  demoActivateUrl = "https://www.socure.com/demo"

  reCaptcha{
      siteVerifyUrl = "https://recaptchaenterprise.googleapis.com/v1beta1/projects/socure-dashboardv2/assessments"
      privatekey="ENC(jif8M/vndORVQc0VEA6Nrt151mZb1ucMEo/PUWe82dET3ARe61p88ycxNXqbXjIoLgk/m6Lwyjqse3MdWtFMoBaBiUw+0s5e)"
      apiKey="ENC(hmKE0gfGCZKalUfZSibzvAlOosQvSjjYNrH4tcrCdG3zEEGf7q4+bFvWSKD+j0l9PcyD0wTenHls9fkESjtogI7zCxxyDfs=)"
  }

  cors{
    alloweddomains = ["https://admin.socure.com", "https://dashboard.socure.com", "https://demo.socure.com", "https://www.socure.com", "https://riskos.sandbox.socure.com", "https://riskos.socure.com", "https://dashboard-core.socure.com", "https://developer.socure.com"]
  }

  sqs {
    sqsEndpoint="https://sqs.us-east-1.amazonaws.com/"
    s3BucketName=action-auditing-prod
    queueName=action-auditing-prod
    region="us-east-1"
    # batchrun-prod
    kmsKey="""ENC(Hml+gJqMCXpMgnuiqCijvdHDTO/ZJrG1a0IUW3StktXI1KXlA9geYS8yom7MQzO9Ae4pkpfoUNxOS1OAuOrr5WnvQEXywmzpKIK2p2Oeq7j5GaMK+J78g9sqcgft1OZ2RXLp7YuLELsjan0=)"""
    # prods3reader
  }

  jmx{
    port=1098
  }

  client.specific.encryption {
    encryption.context.account_id.key = "socure_account_id"
    kms.ids {
      "us-east-1" = """arn:aws:kms:us-east-1:************:alias/client-specific-encryption-prod"""
      "us-west-1" = """arn:aws:kms:us-west-1:************:alias/client-specific-encryption-prod"""
    }
  }

  document.verfication {
    uploadmaxsize = ********
    uploadrequestsize = ********
  }

  webhook.certificate {
    uploadmaxsize = 100000
    uploadrequestsize = 102400
  }

  max.post.request.size.bytes = ********

  saml2 {
    ssoRedirectDashboardPage = "https://dashboard.socure.com/#!/saml2"
    ssoRedirectDevhubPage = "https://developer.socure.com"
    ssoRedirectRiskOsPage = "https://riskos.socure.com"
  }

  transaction.auditing {
    endpoint="http://transaction-auditing-service"
    endpoint2 = "http://transaction-auditing-service"
    hmac {
      secret.key="""ENC(524crRkartFhRPf4opnvNUC1MiTONpFNliT6JGK0COcb4xqT31jaPCLCoSIAdqIHkhlTMak05A==)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }
    dynamic.control.center{
      s3 {
        bucketName="globalconfig-prod"
      }
      memcached {
        host="vpc-memcached-prod-2020.ps2mlp.cfg.use1.cache.amazonaws.com"
        port=11211
        ttl=86400
      }
      local.cache {
        timeout.minutes=2
      }
    }
    metrics.enabled = false
  }

  transaction-auditing {
    threadpool {
      poolSize=30
    }

    aws {

      maxRetries = 10

      primary {
        sqs {
          region=us-east-1
          transaction {
            queueName=transaction-auditing-prod
            waitTimeSeconds=20
            maxBatchSize=10
            maxBufferSize=60
          }
          third-party {
            queueName=third-party-transaction-auditing-prod
            waitTimeSeconds=20
            maxBatchSize=10
            maxBufferSize=60
          }
          producer {
            #maximum number of messages being processed by AmazonSQSAsync at the same time
            transaction {
              maxInFlight: 15
            }
            third-party {
              maxInFlight: 15
            }
          }
        }
      }

      fallback0 {
        sqs {
          region=us-west-2
          transaction {
            queueName=transaction-auditing-prod
            waitTimeSeconds=20
            maxBatchSize=10
            maxBufferSize=60
          }
          third-party {
            queueName=third-party-transaction-auditing-prod
            waitTimeSeconds=20
            maxBatchSize=10
            maxBufferSize=60
          }
          producer {
            #maximum number of messages being processed by AmazonSQSAsync at the same time
            transaction {
              maxInFlight: 15
            }
            third-party {
              maxInFlight: 15
            }
          }
        }
      }

      fallback1 {
        sqs {
          region=us-east-2
          transaction {
            queueName=transaction-auditing-prod
            waitTimeSeconds=20
            maxBatchSize=10
            maxBufferSize=60
          }
          third-party {
            queueName=third-party-transaction-auditing-prod
            waitTimeSeconds=20
            maxBatchSize=10
            maxBufferSize=60
          }
          producer {
            #maximum number of messages being processed by AmazonSQSAsync at the same time
            transaction {
              maxInFlight: 15
            }
            third-party {
              maxInFlight: 15
            }
          }
        }
      }

      s3 {
        largefiles {
          folder="sqs-storage-prod"
        }
        third-party {
          region=us-east-1
          bucket="thirdparty-stats-prod"
        }
      }

      sqs {
        backoff {
          # seconds
          min: 2
          max: 32
        }
      }
    }
  }

  file.storage.download {
    endpoint="http://file-storage-download"
    endpoint2="http://file-storage-download"
    groupName="DVServicesRamp"
    flagName="FileDownload_Ramp"
    hmac {
      secret.key="""ENC(tusO8GGidjJJ64AGe2qCwfoPQv93PJApf4Ck88ZlyJ9SDNlthAO6Gq3V/y6ygPriUuRbcy8VnWn0UAh/slbnOQ==)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }
    metrics.enabled = false
  }

  file.storage.upload.endpoint = "https://upload.socure.com"
  file.storage.domain.skip.secret = """ENC(SPbX472S6kW3nOugUr9p6iC2R/hpZ8gx6yS/rZ9/kZSdxBf7WvWBYoqYxNs/CBZnVqUUfaHhc+Zm6hbclHF4i4zCGK4=)"""

  authentication.service {
    endpoint = "http://authentication-service"
    endpoint2="http://authentication-service"
    groupName="UXServicesRamp"
    flagName="AuthenticationService_Ramp"

    hmac {
      secret.key="""ENC(Ieysuld9/sR8znzSWoZplgtuZHfpB0y35eTdVlFKCYbZhn5cBpqrznBY259hn7q+e2c3XcgPG/fyFYu6rQyDQg==)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }
  }
#pointing both to EKS (instead of CreateClientV2 change)
reasoncode.service {
    endpoint="http://reasoncode-service" 
    endpoint2="http://reasoncode-service"
    hmac {
      secret.key="""ENC(+f3lM4zPCfP0ZNV7u14v738Bt28mJ1TagT38zCdKy6jYNiUghvlpOBHF9Mhr5C3j8Xr3agEi597uxrwIHY263Q==)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }
    metrics.enabled = false
  }

  transaction.search {
      endpoint = "http://transaction-search-service"
      endpoint2="http://transaction-search-service"
      groupName="UXServicesRamp"
      flagName="TransactionSearchService_Ramp"

      hmac {
          maxResultSize = 100
          realm = "PII"
          version = "1.0"
          strength = 512
          aws.secrets.manager.id = "transaction-search-service/prod/hmac/querying/pii"
          secret.refresh.interval = 5000
      }
  }

  transaction.resolved.entity.service {
    endpoint = "http://transaction-resolved-entity-service"
    endpoint2 = "http://transaction-resolved-entity-service"
    groupName="IdplusServicesRamp"
    flagName="TransactionResolvedEntityService_Ramp"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl=5
      time.interval=5
      strength=512
      aws.secrets.manager.id="transaction-resolved-entity-service/prod/hmac"
      secret.refresh.interval=5000
    }
  }

  #================ ThirdParty Reader Service config ================#
  thirdparty.reader.service {
    endpoint = "http://thirdparty-reader-service"
    endpoint2 = "http://thirdparty-reader-service"

    hmac {
      realm="Socure"
      version="1.0"
      ttl=5
      time.interval=5
      strength=512
      aws.secrets.manager.id="thirdparty-reader-service/prod/hmac"
      secret.refresh.interval=5000
    }
    metrics.enabled = false
  }
  #================ ThirdParty Reader Service config ================#

#================= Model Config V3 #=================

#Correlation Model - Name vs Address - US (V3.0)
datasci.model.defaults.v_3_0_0.cs_name_address.identifier="name_address_WP_HD_20190424_grid2_model_53"
datasci.model.defaults.v_3_0_0.cs_name_address.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_address.negative.threshold=0.20

#Correlation Model - Name vs Email - US (V3.0)
datasci.model.defaults.v_3_0_0.cs_name_email.identifier="name_email_30_corr_fuzzymatch_model_2"
datasci.model.defaults.v_3_0_0.cs_name_email.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_email.negative.threshold=0.20

#Correlation Model - Name vs Phone - US (V3.0)
datasci.model.defaults.v_3_0_0.cs_name_phone.identifier="name_phone_30_corr_fuzzymatch_model_5"
datasci.model.defaults.v_3_0_0.cs_name_phone.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_phone.negative.threshold=0.20

#Correlation Model - Name vs Address - International (=V3.0)
datasci.model.defaults.v_3_0_0.cs_name_address_int.identifier="name_address_WP_HD_20190424_grid2_model_53"
datasci.model.defaults.v_3_0_0.cs_name_address_int.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_address_int.negative.threshold=0.20

#Correlation Model - Name vs Email - International (=V3.0)
datasci.model.defaults.v_3_0_0.cs_name_email_int.identifier="name_email_30_corr_fuzzymatch_model_2"
datasci.model.defaults.v_3_0_0.cs_name_email_int.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_email_int.negative.threshold=0.20

#Correlation Model - Name vs Phone - International (=V3.0)
datasci.model.defaults.v_3_0_0.cs_name_phone_int.identifier="name_phone_30_corr_fuzzymatch_model_5"
datasci.model.defaults.v_3_0_0.cs_name_phone_int.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_phone_int.negative.threshold=0.20

#Address Risk US model
datasci.model.defaults.v_3_0_0.pii_risk_address_us.identifier="AddressRisk_GBM_WP_NS_FM_20190405"
datasci.model.defaults.v_3_0_0.pii_risk_address_us.low_risk.threshold=0.591204450670601
datasci.model.defaults.v_3_0_0.pii_risk_address_us.high_risk.threshold=0.760350271758428

#Email Risk US model
datasci.model.defaults.v_3_0_0.pii_risk_email_us.identifier="GBM_EmailRisk_WP_FC_NS_FM_2019_05_21"
datasci.model.defaults.v_3_0_0.pii_risk_email_us.low_risk.threshold=0.803054470577534
datasci.model.defaults.v_3_0_0.pii_risk_email_us.high_risk.threshold=0.939723723880854

#Phone Risk US model
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.identifier="phone_riskmodel_PF_WP_NS_20190412"
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.low_risk.threshold=0.834893108474672
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.high_risk.threshold=0.923285373677192

#Address Risk International model
datasci.model.defaults.v_3_0_0.pii_risk_address_int.identifier="AddressRisk_GBM_WP_NS_FM_20190405"
datasci.model.defaults.v_3_0_0.pii_risk_address_int.low_risk.threshold=0.801802614644041
datasci.model.defaults.v_3_0_0.pii_risk_address_int.high_risk.threshold=0.847327892956023

#Email Risk International model
datasci.model.defaults.v_3_0_0.pii_risk_email_int.identifier="GBM_EmailRisk_WP_FC_NS_FM_2019_05_21"
datasci.model.defaults.v_3_0_0.pii_risk_email_int.low_risk.threshold=0.787949596714919
datasci.model.defaults.v_3_0_0.pii_risk_email_int.high_risk.threshold=0.857516116761921

#Phone Risk International model
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.identifier="phone_riskmodel_PF_WP_NS_20190412"
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.low_risk.threshold=0.929784963657676
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.high_risk.threshold=0.97420772440818

#================= Model Config V3 #=================

  redirectURLs {
		docs="https://developer.socure.com/SetSID?session=",
		demo="https://demo.socure.com/SetSID?session=",
		platform = "https://riskos.socure.com/api/sid/set?session="
	}
  sameSite="Strict"

  websdk.customization.uploadConfig {
    fileMaxSize=100000
    requestMaxSize=102400
  }

  entity.monitoring {
    endpoint="http://entity-monitoring-service"
    endpoint2="http://entity-monitoring-service"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "entity-monitoring-service/prod/hmac"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
  }

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-prod"
    }
    memcached {
      host="vpc-memcached-prod-2020.ps2mlp.cfg.use1.cache.amazonaws.com"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  batch.job {
    endpoint="http://batch-job-service"
    endpoint2="http://batch-job-service"
    groupName="UXServicesRamp"
    flagName="BatchJobService_Ramp"

    hmac {
        ttl = 5
        time.interval = 5
        strength = 512
        aws.secrets.manager.id = "batchjob-service/prod/hmac"
        secret.refresh.interval = 5000
        realm="Socure"
        version = "1.0"
      }
      aws {
          s3 {
             bucket.name = "batch-job-storage-prod"
             kms.key = """ENC(r+essdswGHMH/JMaSxbrBXYRcc9GnwBl1/zjQ+LlfkgAGGlZw2XFV9zNA4IA5ng5aAbjLDTMKxIR2DgPz1/0KaE5Eh6KLDhpvTXCJ3LZhmYDZxNjwWPJ4VZJcgCGFdgrP0RD2n/b7BrqNrI=)"""
          }
          region="us-east-1"
      }
      uploadMaxSize=********0
      requestMaxSize=********0
  }

  customer.customization {
    endpoint="http://customer-asset-storage"
    endpoint2="http://customer-asset-storage"
    groupName="DVServicesRamp"
    flagName="CustomerAssetStorage_Ramp"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "customer-asset-storage-service/prod/hmac"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
    metrics.enabled = false
  }

  file.upload {
    bucket = "client-datafile-prod"
    kms_id = "arn:aws:kms:us-east-1:************:alias/client-datafile-prod"
    uploadMaxSize=1073741824
    requestMaxSize=1078984704
  }

  badLoginConfig {
    maxTry=3
    atoLimit=10
  }

  document.manager {
    endpoint="http://document-manager"
    endpoint2="http://document-manager"
    groupName="DVServicesRamp"
    flagName="DocumentManager_Ramp"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id="document-manager-service/prod/hmac"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
    memcached {
      host=vpc-memcached-prod-2020.ps2mlp.cfg.use1.cache.amazonaws.com
      port=11211
    }
  }

  stepUp.service {
    endpoint="https://stepup.socure.com"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "stepup/prod/hmac"
      secret.refresh.interval = 5000
      realm = "Socure"
      version = "1.0"
    }
    metrics.enabled = false
  }

  device.risk {
    url="https://dvnfo.com"
  }

  memcached.endpoint="vpc-memcached-prod-2020.ps2mlp.cfg.use1.cache.amazonaws.com"
  memcached.port=11211

  decision.service {
    endpoint="http://decision-service"
    endpoint2="http://decision-service"
    groupName="UXServicesRamp"
    flagName="DecisionService_Ramp"
    bccMails = ["<EMAIL>"]
  }

  globalwatchlist.service {
    endpoint = "http://watchlist-private-service/api"
    endpoint2="http://watchlist-private-service/api"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl=5
      time.interval=5
      strength=512
      aws.secrets.manager.id="watchlist-private-service/prod/hmac"
      secret.refresh.interval=5000
    }
  }

  watchlist.in.house {
    endpoint = "http://watchlist-in-house-ingestion"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      realm="Socure"
      version="1.0"
      aws.secrets.manager.id = "watchlist-in-house-ingestion/prod/9199c1bb"
      secret.refresh.interval = 5000
    }
  }

  rate.limiter.token.bucket {
      redis.uri = "redis://prod-api-rate-limit-admin-dashboard.ps2mlp.ng.0001.use1.cache.amazonaws.com"
      local.cache.max.size = 200 //used for maintaining list of policies
      cache.ttl = "1 minute"
      methods = ["POST","PUT","DELETE"]
      excludeUrls = ["/saml2"]
  }

  txn.case.workflow.service {
    endpoint = "http://txn-case-workflow-service"
    endpoint2 = "http://txn-case-workflow-service"
    groupName="IdplusServicesRamp"
    flagName="TxnCaseWorkflowService_Ramp"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "txn-case-workflow-service/prod/hmac"
      secret.refresh.interval = 5000
    }
  }

  txn.case.workflow.manager {
    endpoint = "http://txn-case-workflow-manager"
    endpoint2 = "http://txn-case-workflow-manager"
    groupName="IdplusServicesRamp"
    flagName="TxnCaseWorkflowManager_Ramp"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "txn-case-workflow-manager/prod/hmac"
      secret.refresh.interval = 5000
    }
  }

  entity.feedback.reader {
    endpoint = "http://entity-feedback-service"
    endpoint2 = "http://entity-feedback-service"
    groupName="IdplusServicesRamp"
    flagName="EntityFeedbackService_Ramp"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "entity-feedback-service/prod/hmac"
      secret.refresh.interval = 5000
    }
  }

  entity.feedback.worker {
    endpoint = "http://entity-feedback-worker"
    endpoint2 = "http://entity-feedback-worker"
    groupName="IdplusServicesRamp"
    flagName="EntityFeedbackWorker_Ramp"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "entity-feedback-worker/prod/hmac"
      secret.refresh.interval = 5000
    }
  }
  custom.watchlist.manager {
    endpoint = "http://custom-wl-manager"
    endpoint2 = "http://custom-wl-manager"
    groupName="IdplusServicesRamp"
    flagName="CustomWLManager_Ramp"
     hmac {
        realm = "Socure"
        version = "1.0"
        ttl=5
        time.interval=5
        strength=512
        aws.secrets.manager.id="custom-wl-manager/prod/hmac"
        secret.refresh.interval=5000
      }
}
custom.watchlist.s3 {
      bucket.name="custom-wl-file-storage-prod"
      kms.key="a25ddcc5-9493-4ba6-9935-aadf39912da5"
      path {
        base_path="input"
      }
    }

custom.watchlist.file.maxsize=200

  transaction.search {
    fields-and-boost {
      "parameters.pii.firstName" = 5.0,
      "parameters.pii.surName" = 5.0,
      "parameters.pii.city" = 1.0,
      "parameters.modules" = 1.0,
      "parameters.pii.email" = 4.0,
      "parameters.pii.email.domain" = 1.0,
      "parameters.pii.email.user" = 4.0,
      "parameters.pii.mobileNumber" = 4.0,
      "parameters.pii.mobileNumber.ten" = 4.0,
      "parameters.pii.nationalId" = 4.0,
      "parameters.pii.nationalId.four" = 2.0,
      "parameters.pii.zip" = 1.0,
      "parameters.pii.state" = 1.0,
      "parameters.pii.country" = 1.0,
      "parameters.pii.customerUserId" = 4.0,
      "parameters.pii.userId" = 4.0,
      "parameters.pii.firstName.partial" = 1.0,
      "parameters.pii.firstName.partial_extended" = 1.0,
      "parameters.pii.surName.partial" = 1.0,
      "parameters.pii.surName.partial_extended" = 1.0,
      "parameters.pii.physicalAddress.partial" = 1.0,
      "parameters.pii.physicalAddress2.partial" = 1.0,
      "parameters.pii.ipAddress" = 2.0,
      "parameters.pii.driverLicense" = 4.0,
      "parameters.documentUuid" = 4.0,
      "parameters.pii.companyName" = 1.0,
      "response.decisionResult" = 4.0,
      "response.reasonCodes" = 4.0,
      "parameters.businessName" = 1.0,
      "parameters.ein" = 2.0,
      "parameters.ein.digits" = 2.0,
      "parameters.payments.account.accountNumber" = 2.0,
      "parameters.payments.account.routingNumber" = 2.0,
      "parameters.businessPhone" = 1.0,
      "parameters.businessPhone.partial" = 1.0,
      "parameters.entityName" = 2.0
    }
    date.patterns = [
      "yyyy-MM-dd",
      "yyyy/MM/dd",
      "MM/dd/yyyy",
      "MM-dd-yyyy",
      "MMddyyyy",
      "yyyyMMdd"
    ]
  }

  #============= TowerData =============#
  towerdata.endpoint="https://api.towerdata.com/v5/eppend"
  towerdata.apiKey="""ENC(MhpWFqcVv+QOW/2PJNY8qAKSOOOmPqIODrnJ+Ljmh1nS89dXqH7fw6gUnVrMcpSMqcawyMdv8j4PyXS5Wu7h6A==)"""

  #===================docv-berbix==========================#
  berbix.v1 {
      endpoint="http://docv-bb-backend"
      hmac {
          secret.key="""ENC(LQJc4ndfNa4pnYDt0A8RA/TSQ0csN3skZhqGh5lTSCc716yCrUgN8vSRDm8CT3QfE7OWiYlZXBXjlAB2OgjAmEeaK7ht+PCDeC3hNQ==)"""
          strength=512
          realm="Socure"
          version="1.0"
      }
  }

  #============= DocV Orchestra ========#
  document.verification {
   docvOrchestra {
       endpoint = "http://docv-orchestra-service/"
       endpoint2 = "http://docv-orchestra-service/"
       hmac {
          realm="Socure"
          version = "1.0"
          strength=512
          aws.secrets.manager.id="dv-orchestra/prod/hmac"
        }
        metrics.enabled = false
        dynamic.control.center {
            s3 {
              bucketName = "globalconfig-prod"
            }
            memcached {
              host=vpc-memcached-prod-2020.ps2mlp.cfg.use1.cache.amazonaws.com
              port=11211
              ttl=86400
            }
            local {
              cache.timeout.minutes=2
            }
          }
    }
  }


  aws.quicksight {
    region = "us-east-1"
    awsaccountid = "************"
    session.minutes = 600,
    usernamespace = "default"
    dashboards {
      "overview" = "41bf63ac-8f14-4da1-bc9a-75c689b82c9c",
      "sponsorbankoverview" = "2f452663-7bcb-48a9-a595-172aaa4f7d7c",
      "system" = "0c3ff078-b5f4-4126-80b6-576c9e648fa3",
      "reasoncodes" = "dec8870a-ee0f-43dc-90f3-8b3c5b42a0e9",
      "kyc" = "e5225f44-3d40-4928-bc26-ff59cf415568",
      "docv" = "3d9879fa-8a39-451f-af25-95e108e90cea",
      "decision" = "fa0bff90-3fb9-4518-8940-01bd0812e0a1",
      "fraudscore" = "68c6fc11-3cb9-47e6-9d54-0c058b0796c7",
      "synthetic" = "2f606058-bdf1-4dd4-8f09-8afa8a2e9add",
      "emailrisk" = "f65c30d3-e2c1-4fd8-be6c-39a6cae59ed1",
      "phonerisk" = "5412a68e-ac7c-4fc3-9dac-93a5ce9fa020",
      "addressrisk" = "f6605fbf-19a2-4cd5-8541-54850ad63a3e"
   }
  }
  
  batch.transaction {
   aws {
     s3 {
       bucket.name = "batch-data-processing-prod"
     }
   }
  }

  # watchlist case file upload limit.
  case.file.upload {
    uploadMaxSize = ********
    requestMaxSize = ********
  }

  negPos {
     url = "label-list-service"
     port = 81
     useTLS = false
  }

  launchdarkly {
     sdk.key = "ENC(Sa2DsunYtlXFkjKPzL3yrLWzpFVCwbWUk6BMhltiCMt/mqibfYAYtyW53DS/v6Z2tSLRTzY673SRBy3gZl4utTaYEgBT1Q1C)"
     use.fedramp.version = false
  }

  # watchlist case closure
  watchlist.case.closure {
    bucket.name = "wl-case-mgmt-actions-prod-8635583e-us-east-1"
    base.path = "bulkCaseClosure/input/"
  }

  watchlist {
    audit {
      manager {
        max.retry.count = 3
        page.size = 100
        endpoint = "http://watchlist-audit-manager"
        hmac {
          realm="Socure"
          version="1.0"
          ttl=5
          time.interval=5
          strength=512
          aws.secrets.manager.id="watchlist-audit-manager/prod/hmac-c7da8a19"
          secret.refresh.interval=5000
        }
      }
    }
  }

  mfa.orchestrator {
    host = "http://mfa-orchestrator-service"
  }
  chatBot {
    endpoint = "https://agent-devhub.webapps.us-east-1.prod.socure.link"
    streamPath = "/chat/riskos/stream"
    feedbackPath = "/chat/$id1/message/$id2/feedback"
  }
  explainability {
    url = "explainability-service.webapps.us-east-1.product-dev.socure.link"
    port = 443
  }

  dashboardUrls{
    platform = ["https://riskos.sandbox.socure.com","https://riskos.socure.com"]
    demo = ["https://demo.socure.com"]
  }
  socure.translator {
    host = "http://socure-translator-service"
  }
}
