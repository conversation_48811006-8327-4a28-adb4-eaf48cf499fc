image:
  repository: fips-registry.us-east-1.build.socure.link/idp/socure-translator-service
  tag: fips-0.1.0-141680-64fec380

serviceAccount:
  name: "socure-translator-service-dev"
  annotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/eks-irsa-383a382-dev-9b5815c4"

application:
  env:
    CONFIGURATION_NAME: "socure-translator-service"
    CONFIGURATION_VERSION: mscv_64fec380
    JAVA_TOOL_OPTIONS: "-XX:InitialRAMPercentage=50.0 -XX:MaxRAMPercentage=85.0 -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:NewRatio=1 -Dorg.bouncycastle.jca.enable_jks=true -javaagent:/opt/socure/dd-java-agent.jar -Dnet.spy.log.LoggerImpl=net.spy.memcached.compat.log.SLF4JLogger"

deployment:
  podVolumes:
    - name: tmp
      emptyDir: {}
  podVolumeMounts:
    - name: tmp
      mountPath: /tmp
  nodeSelector:
    #karpenter.sh/provisioner-name: default-amd64-ondemand
    kubernetes.io/os: linux
    karpenter.sh/provisioner-name: null
    karpenter.sh/nodepool: apps-default

istio:
  enabled: true
  hosts:
    - socure-translator-service.webapps.us-east-1.product-dev.socure.link
  svcPort: 80
  private:
    gateway: private-gw
  authorizationPolicy:
    enabled: true
    pa:
      enabled: true
      serviceAccounts:
        - admin-dashboard-dev
        - idplus-service-dev
        - idplus-hsm-dev
