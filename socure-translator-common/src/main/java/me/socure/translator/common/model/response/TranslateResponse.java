package me.socure.translator.common.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@Schema(description = "Represents the response containing a list of translated texts")
public class TranslateResponse {
    @Schema(description = "A list of translated texts with optional IDs", required = true)
    private final List<TranslatedTexts> translatedTexts;
}
