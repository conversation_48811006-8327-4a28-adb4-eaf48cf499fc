package me.socure.translator.common.model.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "A generic API response wrapper that contains response status and data")
public class APIResponse<T> {

    public enum Status {
        OK,
        ERROR;
    }

    @Schema(description = "The status of the API response", required = true, example = "OK")
    private final Status status;
    @Schema(description = "The data returned in the response")
    private final T data;

}
