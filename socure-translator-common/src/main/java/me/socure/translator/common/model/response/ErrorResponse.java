package me.socure.translator.common.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.jackson.Jacksonized;

@Jacksonized
@Data
@Builder
@Schema(description = "Represents an error response with a status code and a message")
public class ErrorResponse {
    @NonNull
    @Schema(description = "HTTP status code representing the error", required = true, example = "400")
    private Integer code;

    @NonNull
    @Schema(description = "Detailed error message explaining the reason for the error", required = true, example = "INVALID_ACCOUNT_ID")
    private String message;
}
