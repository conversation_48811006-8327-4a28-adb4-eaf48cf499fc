package me.socure.translator.common.model.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Represents translated texts with an optional ID if given in input")
public class TranslatedTexts {

    @Schema(description = "Optional ID associated with the translated texts", example = "1938748")
    private final String id;

    @Schema(description = "List of translated texts", required = true, example = "[\"This is translated text in desired language\", \"This is the correct translated text\"]")
    private final List<String> texts;
}
