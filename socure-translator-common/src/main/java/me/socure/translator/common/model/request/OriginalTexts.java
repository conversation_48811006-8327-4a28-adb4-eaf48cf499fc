package me.socure.translator.common.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.jackson.Jacksonized;
import java.util.List;

@Data
@Jacksonized
@Builder
@Schema(description = "Represents the original texts for translation")
public class OriginalTexts {

    @Schema(description = "Unique ID for the original texts (optional)", example = "1938748")
    private final String id;

    @NonNull
    @Schema(description = "List of original texts to be translated", required = true, example = "[\"Este es un texto traducido en el idioma deseado\", \"Este es el texto traducido correctamente\"]")
    private final List<String> texts;
}
