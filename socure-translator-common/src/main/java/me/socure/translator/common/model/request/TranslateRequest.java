package me.socure.translator.common.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.jackson.Jacksonized;
import java.util.List;

@Data
@Jacksonized
@Builder
@Schema(description = "Represents a request to translate texts into another language")
public class TranslateRequest {

    @NonNull
    @Schema(description = "Target language code for translation", required = true, example = "en")
    private final String toLanguage;

    @NonNull
    @Schema(description = "Transaction ID for which the translation is required", required = true, example = "f6ed9c72-a86b-4981-8cce-2d0d33e28e60")
    private final String transactionId;

    @NonNull
    @Schema(description = "List of original texts with optional IDs to be translated", required = true)
    private final List<OriginalTexts> originalTexts;

    @NonNull
    @Schema(description = "Account ID for which the translation is required", required = true, example = "4845")
    private final String accountId;
}
